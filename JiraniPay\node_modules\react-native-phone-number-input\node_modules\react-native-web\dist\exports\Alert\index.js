function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

/**
 * Copyright (c) 2016-present, <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * 
 */

var Alert = function () {
  function Alert() {
    _classCallCheck(this, Alert);
  }

  Alert.alert = function alert() {};

  return Alert;
}();

export default Alert;