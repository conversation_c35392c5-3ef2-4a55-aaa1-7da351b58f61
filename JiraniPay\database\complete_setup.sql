-- =====================================================
-- JiraniPay Complete Database Setup Script
-- =====================================================
-- This script creates all necessary tables, indexes, and security policies
-- for the JiraniPay fintech application.
--
-- INSTRUCTIONS:
-- 1. Go to app.supabase.com
-- 2. Select your JiraniPay project
-- 3. Click "SQL Editor" in the left sidebar
-- 4. Click "New Query"
-- 5. Copy and paste this entire script
-- 6. Click "Run" to execute
-- =====================================================

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- =====================================================
-- USER PROFILES TABLE
-- =====================================================
-- Stores additional user information beyond Supabase Auth
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    full_name TEXT NOT NULL,
    phone_number TEXT UNIQUE NOT NULL,
    country_code TEXT NOT NULL DEFAULT 'UG',
    preferred_language TEXT DEFAULT 'en',
    date_of_birth DATE,
    profile_picture_url TEXT,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_level TEXT DEFAULT 'basic', -- 'basic', 'verified', 'premium'
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- USER PREFERENCES TABLE
-- =====================================================
-- Stores user app preferences and settings
CREATE TABLE IF NOT EXISTS public.user_preferences (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    biometric_enabled BOOLEAN DEFAULT FALSE,
    notifications_enabled BOOLEAN DEFAULT TRUE,
    sms_notifications BOOLEAN DEFAULT TRUE,
    email_notifications BOOLEAN DEFAULT TRUE,
    dark_mode_enabled BOOLEAN DEFAULT FALSE,
    preferred_currency TEXT DEFAULT 'UGX',
    transaction_limit_daily DECIMAL(15,2) DEFAULT 1000000, -- 1M UGX default
    transaction_limit_monthly DECIMAL(15,2) DEFAULT ********, -- 10M UGX default
    security_pin_enabled BOOLEAN DEFAULT FALSE,
    auto_logout_minutes INTEGER DEFAULT 30,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- PAYMENT ACCOUNTS TABLE
-- =====================================================
-- Stores user's linked payment methods (mobile money, bank accounts, etc.)
CREATE TABLE IF NOT EXISTS public.payment_accounts (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    account_type TEXT NOT NULL, -- 'mobile_money', 'bank_account', 'wallet', 'card'
    provider_name TEXT NOT NULL, -- 'MTN Mobile Money', 'Airtel Money', 'Bank of Uganda', etc.
    account_number TEXT NOT NULL,
    account_name TEXT,
    account_holder_name TEXT,
    currency TEXT DEFAULT 'UGX',
    is_primary BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    balance DECIMAL(15,2) DEFAULT 0.00,
    last_balance_update TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}', -- Store provider-specific data
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure only one primary account per user per type
    CONSTRAINT unique_primary_per_user_type UNIQUE(user_id, account_type, is_primary) DEFERRABLE INITIALLY DEFERRED
);

-- =====================================================
-- TRANSACTIONS TABLE
-- =====================================================
-- Stores all financial transactions
CREATE TABLE IF NOT EXISTS public.transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    transaction_type TEXT NOT NULL, -- 'bill_payment', 'airtime', 'transfer', 'deposit', 'withdrawal'
    category TEXT, -- 'utilities', 'mobile', 'internet', 'tv', 'water', 'electricity'
    amount DECIMAL(15,2) NOT NULL,
    currency TEXT NOT NULL DEFAULT 'UGX',
    fee_amount DECIMAL(15,2) DEFAULT 0.00,
    total_amount DECIMAL(15,2) GENERATED ALWAYS AS (amount + fee_amount) STORED,
    status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'processing', 'completed', 'failed', 'cancelled'
    reference_number TEXT UNIQUE,
    external_reference TEXT, -- Provider's reference number
    description TEXT,
    recipient_info JSONB, -- Store recipient details
    payment_method_id UUID REFERENCES public.payment_accounts(id),
    provider_name TEXT, -- Which service provider (MTN, Umeme, NWSC, etc.)
    provider_response JSONB, -- Store provider API responses
    failure_reason TEXT,
    processed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- BILL PROVIDERS TABLE
-- =====================================================
-- Stores available bill payment providers
CREATE TABLE IF NOT EXISTS public.bill_providers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL, -- 'utilities', 'telecom', 'tv', 'internet'
    country_code TEXT NOT NULL,
    logo_url TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    api_endpoint TEXT,
    supported_currencies TEXT[] DEFAULT ARRAY['UGX'],
    min_amount DECIMAL(15,2) DEFAULT 1000,
    max_amount DECIMAL(15,2) DEFAULT ********,
    fee_structure JSONB, -- Store fee calculation rules
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- USER SESSIONS TABLE
-- =====================================================
-- Track user sessions for security and analytics
CREATE TABLE IF NOT EXISTS public.user_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    session_token TEXT,
    device_info JSONB,
    ip_address INET,
    location_info JSONB,
    login_method TEXT, -- 'password', 'otp', 'biometric'
    is_active BOOLEAN DEFAULT TRUE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ended_at TIMESTAMP WITH TIME ZONE
);

-- =====================================================
-- AUDIT LOG TABLE
-- =====================================================
-- Track important user actions for security and compliance
CREATE TABLE IF NOT EXISTS public.audit_logs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action TEXT NOT NULL, -- 'login', 'logout', 'transaction', 'profile_update', etc.
    resource_type TEXT, -- 'user', 'transaction', 'payment_account'
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- User Profiles indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_phone ON public.user_profiles(phone_number);
CREATE INDEX IF NOT EXISTS idx_user_profiles_country ON public.user_profiles(country_code);
CREATE INDEX IF NOT EXISTS idx_user_profiles_verification ON public.user_profiles(is_verified, verification_level);

-- Payment Accounts indexes
CREATE INDEX IF NOT EXISTS idx_payment_accounts_user ON public.payment_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_accounts_type ON public.payment_accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_payment_accounts_provider ON public.payment_accounts(provider_name);
CREATE INDEX IF NOT EXISTS idx_payment_accounts_active ON public.payment_accounts(is_active);
CREATE INDEX IF NOT EXISTS idx_payment_accounts_primary ON public.payment_accounts(user_id, is_primary) WHERE is_primary = true;

-- Transactions indexes
CREATE INDEX IF NOT EXISTS idx_transactions_user ON public.transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON public.transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON public.transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_created ON public.transactions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_transactions_reference ON public.transactions(reference_number);
CREATE INDEX IF NOT EXISTS idx_transactions_user_created ON public.transactions(user_id, created_at DESC);
CREATE INDEX IF NOT EXISTS idx_transactions_user_status ON public.transactions(user_id, status);

-- Bill Providers indexes
CREATE INDEX IF NOT EXISTS idx_bill_providers_country ON public.bill_providers(country_code);
CREATE INDEX IF NOT EXISTS idx_bill_providers_category ON public.bill_providers(category);
CREATE INDEX IF NOT EXISTS idx_bill_providers_active ON public.bill_providers(is_active);

-- User Sessions indexes
CREATE INDEX IF NOT EXISTS idx_user_sessions_user ON public.user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON public.user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_created ON public.user_sessions(created_at DESC);

-- Audit Logs indexes
CREATE INDEX IF NOT EXISTS idx_audit_logs_user ON public.audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON public.audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created ON public.audit_logs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON public.audit_logs(resource_type, resource_id);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bill_providers ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.audit_logs ENABLE ROW LEVEL SECURITY;

-- User Profiles policies
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- User Preferences policies
CREATE POLICY "Users can manage own preferences" ON public.user_preferences
    FOR ALL USING (auth.uid() = id);

-- Payment Accounts policies
CREATE POLICY "Users can view own payment accounts" ON public.payment_accounts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own payment accounts" ON public.payment_accounts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own payment accounts" ON public.payment_accounts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own payment accounts" ON public.payment_accounts
    FOR DELETE USING (auth.uid() = user_id);

-- Transactions policies
CREATE POLICY "Users can view own transactions" ON public.transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own transactions" ON public.transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Bill Providers policies (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view bill providers" ON public.bill_providers
    FOR SELECT USING (auth.role() = 'authenticated');

-- User Sessions policies
CREATE POLICY "Users can view own sessions" ON public.user_sessions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sessions" ON public.user_sessions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own sessions" ON public.user_sessions
    FOR UPDATE USING (auth.uid() = user_id);

-- Audit Logs policies (read-only for own logs)
CREATE POLICY "Users can view own audit logs" ON public.audit_logs
    FOR SELECT USING (auth.uid() = user_id);

-- =====================================================
-- FUNCTIONS AND TRIGGERS
-- =====================================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers to relevant tables
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_user_preferences_updated_at BEFORE UPDATE ON public.user_preferences
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_payment_accounts_updated_at BEFORE UPDATE ON public.payment_accounts
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON public.transactions
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();

-- Function to generate transaction reference numbers
CREATE OR REPLACE FUNCTION public.generate_transaction_reference()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.reference_number IS NULL THEN
        NEW.reference_number := 'JP' || TO_CHAR(NOW(), 'YYYYMMDD') || LPAD(EXTRACT(EPOCH FROM NOW())::TEXT, 10, '0') || LPAD(FLOOR(RANDOM() * 1000)::TEXT, 3, '0');
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add reference number generation trigger
CREATE TRIGGER generate_transaction_reference_trigger BEFORE INSERT ON public.transactions
    FOR EACH ROW EXECUTE FUNCTION public.generate_transaction_reference();

-- =====================================================
-- SAMPLE DATA FOR TESTING
-- =====================================================

-- Insert sample bill providers for East African countries
INSERT INTO public.bill_providers (name, category, country_code, logo_url, is_active, supported_currencies, min_amount, max_amount) VALUES
-- Uganda
('Umeme (Electricity)', 'utilities', 'UG', null, true, ARRAY['UGX'], 5000, 5000000),
('National Water and Sewerage Corporation', 'utilities', 'UG', null, true, ARRAY['UGX'], 2000, 2000000),
('MTN Uganda', 'telecom', 'UG', null, true, ARRAY['UGX'], 500, 500000),
('Airtel Uganda', 'telecom', 'UG', null, true, ARRAY['UGX'], 500, 500000),
('DSTV Uganda', 'tv', 'UG', null, true, ARRAY['UGX'], 15000, 200000),

-- Kenya
('Kenya Power (KPLC)', 'utilities', 'KE', null, true, ARRAY['KES'], 100, 100000),
('Nairobi Water', 'utilities', 'KE', null, true, ARRAY['KES'], 200, 50000),
('Safaricom', 'telecom', 'KE', null, true, ARRAY['KES'], 10, 10000),
('Airtel Kenya', 'telecom', 'KE', null, true, ARRAY['KES'], 10, 10000),

-- Tanzania
('TANESCO (Electricity)', 'utilities', 'TZ', null, true, ARRAY['TZS'], 1000, 1000000),
('DAWASCO (Water)', 'utilities', 'TZ', null, true, ARRAY['TZS'], 2000, 500000),
('Vodacom Tanzania', 'telecom', 'TZ', null, true, ARRAY['TZS'], 500, 200000),
('Airtel Tanzania', 'telecom', 'TZ', null, true, ARRAY['TZS'], 500, 200000),

-- Rwanda
('EUCL (Electricity)', 'utilities', 'RW', null, true, ARRAY['RWF'], 1000, 500000),
('WASAC (Water)', 'utilities', 'RW', null, true, ARRAY['RWF'], 500, 200000),
('MTN Rwanda', 'telecom', 'RW', null, true, ARRAY['RWF'], 100, 100000),
('Airtel Rwanda', 'telecom', 'RW', null, true, ARRAY['RWF'], 100, 100000);

-- =====================================================
-- COMPLETION MESSAGE
-- =====================================================

-- This will show in the SQL Editor results
SELECT 
    '✅ JiraniPay Database Setup Complete!' as status,
    'All tables, indexes, and security policies have been created successfully.' as message,
    'Your app is now ready for production use!' as next_step;
