// Quick test to verify our dashboard changes
import React from 'react';
import { View, Text, ScrollView, TouchableOpacity, StyleSheet, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';

const { width } = Dimensions.get('window');

const TestDashboard = () => {
  const quickActions = [
    { id: 'buy_data', title: 'Buy Bundles', icon: 'layers-outline' },
    { id: 'send_money', title: 'Send Money', icon: 'arrow-up-circle-outline' },
    { id: 'pay_bills', title: 'Pay Bill', icon: 'receipt-outline' },
    { id: 'mobile_money', title: 'Airtel Money Pay', icon: 'card-outline' },
    { id: 'refer_earn', title: 'Re<PERSON> & Earn', icon: 'person-add-outline' },
    { id: 'withdraw_cash', title: 'Withdraw cash', icon: 'cash-outline' },
    { id: 'qr_pay', title: 'Scan & Pay', icon: 'scan-outline' },
    { id: 'buy_airtime', title: 'Buy Airtime', icon: 'phone-portrait-outline' },
  ];

  return (
    <ScrollView style={styles.container}>
      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <TouchableOpacity>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.actionsGrid}>
          {quickActions.map((action) => (
            <TouchableOpacity key={action.id} style={styles.airtelActionCard}>
              <View style={styles.airtelIconContainer}>
                <Ionicons name={action.icon} size={28} color="#333" />
              </View>
              <Text style={styles.airtelActionText}>{action.title}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Promotional Banners */}
      <View style={styles.promotionsSection}>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.promotionBanner}>
            <LinearGradient
              colors={['#E53E3E', '#C53030']}
              style={styles.promotionGradient}
            >
              <View style={styles.promotionContent}>
                <View style={styles.promotionLeft}>
                  <Text style={styles.promotionTitle}>PAY SCHOOL{'\n'}FEES AND WIN</Text>
                  <View style={styles.promotionPrize}>
                    <Text style={styles.prizeAmount}>UGX{'\n'}100k</Text>
                    <Text style={styles.prizeWinners}>100{'\n'}WINNERS{'\n'}EVERY WEEK</Text>
                  </View>
                </View>
                <View style={styles.promotionRight}>
                  <Text style={styles.studentEmoji}>👩‍🎓</Text>
                </View>
              </View>
            </LinearGradient>
          </View>
        </ScrollView>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: '#f5f5f5', padding: 20 },
  quickActions: { marginBottom: 32 },
  sectionHeader: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 20 },
  sectionTitle: { fontSize: 22, fontWeight: 'bold', color: '#333' },
  viewAllText: { fontSize: 16, color: '#007AFF', fontWeight: '600' },
  actionsGrid: { flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-between', gap: 16 },
  airtelActionCard: {
    width: (width - 72) / 4,
    backgroundColor: 'white',
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 16,
    minHeight: 80,
    justifyContent: 'center',
  },
  airtelIconContainer: { marginBottom: 8 },
  airtelActionText: { fontSize: 11, fontWeight: '500', color: '#333', textAlign: 'center', lineHeight: 14 },
  promotionsSection: { marginBottom: 32 },
  promotionBanner: { width: 280, height: 120, marginRight: 16, borderRadius: 12, overflow: 'hidden' },
  promotionGradient: { flex: 1, padding: 16 },
  promotionContent: { flex: 1, flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center' },
  promotionLeft: { flex: 1 },
  promotionTitle: { color: 'white', fontSize: 16, fontWeight: 'bold', lineHeight: 20, marginBottom: 4 },
  promotionPrize: { flexDirection: 'row', alignItems: 'center', marginTop: 8 },
  prizeAmount: { color: 'white', fontSize: 14, fontWeight: 'bold', marginRight: 12, lineHeight: 16 },
  prizeWinners: { color: 'white', fontSize: 10, fontWeight: '600', lineHeight: 12 },
  promotionRight: { alignItems: 'center', justifyContent: 'center' },
  studentEmoji: { fontSize: 32 },
});

export default TestDashboard;
