export type LocalAuthenticationResult = {
    success: true;
} | {
    success: false;
    error: LocalAuthenticationError;
    warning?: string;
};
export declare enum AuthenticationType {
    /**
     * Indicates fingerprint support.
     */
    FINGERPRINT = 1,
    /**
     * Indicates facial recognition support.
     */
    FACIAL_RECOGNITION = 2,
    /**
     * Indicates iris recognition support.
     * @platform android
     */
    IRIS = 3
}
export declare enum SecurityLevel {
    /**
     * Indicates no enrolled authentication.
     */
    NONE = 0,
    /**
     * Indicates non-biometric authentication (e.g. PIN, Pattern).
     */
    SECRET = 1,
    /**
     * Indicates biometric authentication.
     * @deprecated please use `BIOMETRIC_STRONG` or `BIOMETRIC_WEAK` instead.
     * @hidden
     */
    BIOMETRIC,
    /**
     * Indicates weak biometric authentication. For example, a 2D image-based face unlock.
     * > There are currently no weak biometric authentication options on iOS.
     */
    BIOMETRIC_WEAK = 2,
    /**
     * Indicates strong biometric authentication. For example, a fingerprint scan or 3D face unlock.
     */
    BIOMETRIC_STRONG = 3
}
/**
 * Security level of the biometric authentication to allow.
 * @platform android
 */
export type BiometricsSecurityLevel = 'weak' | 'strong';
export type LocalAuthenticationOptions = {
    /**
     * A message that is shown alongside the TouchID or FaceID prompt.
     */
    promptMessage?: string;
    /**
     * Allows to customize the default `Cancel` label shown.
     */
    cancelLabel?: string;
    /**
     * After several failed attempts the system will fallback to the device passcode. This setting
     * allows you to disable this option and instead handle the fallback yourself. This can be
     * preferable in certain custom authentication workflows. This behaviour maps to using the iOS
     * [`LAPolicyDeviceOwnerAuthenticationWithBiometrics`](https://developer.apple.com/documentation/localauthentication/lapolicy/deviceownerauthenticationwithbiometrics)
     * policy rather than the [`LAPolicyDeviceOwnerAuthentication`](https://developer.apple.com/documentation/localauthentication/lapolicy/deviceownerauthentication?language=objc)
     * policy. Defaults to `false`.
     */
    disableDeviceFallback?: boolean;
    /**
     * Sets a hint to the system for whether to require user confirmation after authentication.
     * This may be ignored by the system if the user has disabled implicit authentication in Settings
     * or if it does not apply to a particular biometric modality. Defaults to `true`.
     * @platform android
     */
    requireConfirmation?: boolean;
    /**
     * Sets the security class of biometric authentication to allow.
     * `strong` allows only Android Class 3 biometrics. For example, a fingerprint or a 3D face scan.
     * `weak` allows both Android Class 3 and Class 2 biometrics. Class 2 biometrics are less secure than Class 3. For example, a camera-based face unlock.
     * @platform android
     * @default 'weak'
     */
    biometricsSecurityLevel?: BiometricsSecurityLevel;
    /**
     * Allows to customize the default `Use Passcode` label shown after several failed
     * authentication attempts. Setting this option to an empty string disables this button from
     * showing in the prompt.
     * @platform ios
     */
    fallbackLabel?: string;
};
/**
 * One of the error values returned by the [`LocalAuthenticationResult`](#localauthenticationresult) object.
 */
export type LocalAuthenticationError = 'not_enrolled' | 'user_cancel' | 'app_cancel' | 'not_available' | 'lockout' | 'no_space' | 'timeout' | 'unable_to_process' | 'unknown' | 'system_cancel' | 'user_fallback' | 'invalid_context' | 'passcode_not_set' | 'authentication_failed';
//# sourceMappingURL=LocalAuthentication.types.d.ts.map