{"name": "@twotalltotems/react-native-otp-input", "version": "1.3.11", "description": "is a tiny JS library for one time passcode (OTP). Supports smart input suggestion on iOS and code autofill on Android (it will be filled when you press the copy button on the SMS notification bar)", "main": "./dist/index.js", "types": "./index.d.ts", "files": ["dist", "index.d.ts"], "scripts": {"start": "node node_modules/react-native/local-cli/cli.js start", "test": "jest", "build": "tsc"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["<rootDir>setup-tests.js"], "testEnvironment": "enzyme", "testEnvironmentOptions": {"enzymeAdapter": "react16"}, "testMatch": ["<rootDir>/__tests__/*.test.js"], "transform": {"^.+\\.jsx$": "babel-jest"}}, "repository": {"type": "git", "url": "git+https://github.com/Twotalltotems/react-native-otp-input.git"}, "keywords": ["react-native", "otp", "otc", "one time password", "one time pin", "one time pincode", "one time code", "sms", "verifcation code", "phone", "verification"], "author": "TTT Studio (https://ttt.studio)", "contributors": ["<PERSON> <<EMAIL>> (https://github.com/BeckyWu220)", "<PERSON> <<EMAIL>> (http://felipe.penya.cl)"], "license": "MIT", "devDependencies": {"@babel/core": "^7.8.4", "@types/jest": "^25.1.4", "@types/react": "^16.9.25", "@types/react-native": "^0.61.23", "babel-jest": "^25.1.0", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.2", "jest": "^25.1.0", "jest-environment-enzyme": "^7.1.2", "jest-enzyme": "^7.1.2", "jsdom": "^16.1.0", "react": "16", "react-dom": "^16.12.0", "react-native": "^0.61.5", "typescript": "^3.8.3"}, "dependencies": {"@react-native-community/clipboard": "^1.2.2"}}