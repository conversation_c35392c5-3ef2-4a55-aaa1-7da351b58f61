import 'react-native-gesture-handler';
import React, { useEffect, useState } from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
// StatusBar removed to fix render error - can be added back later
import { View, ActivityIndicator, StyleSheet } from 'react-native';
import OnboardingScreen from './screens/OnboardingScreen';
import LoginScreen from './screens/LoginScreen';
import RegisterScreen from './screens/RegisterScreen';
import CompleteProfileScreen from './screens/CompleteProfileScreen';
import ForgotPasswordScreen from './screens/ForgotPasswordScreen';
import FAQScreen from './screens/FAQScreen';
import AIAssistantScreen from './screens/AIAssistantScreen';
import MainNavigator from './navigation/MainNavigator';
import authService from './services/authService';
import walletService from './services/walletService';
import { LanguageProvider } from './contexts/LanguageContext';
import { Colors } from './constants/Colors';

const Stack = createStackNavigator();

export default function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      console.log('🚀 Initializing JiraniPay app...');

      // Initialize services
      authService.initialize();
      walletService.initialize();

      // Add auth state listener
      authService.addAuthStateListener((user) => {
        console.log('🔐 Auth state changed, user:', user ? 'authenticated' : 'not authenticated');
        setIsAuthenticated(!!user);
        setIsLoading(false);
      });

      // Check for existing session
      const storedSession = await authService.getStoredSession();
      if (storedSession) {
        // 🚀 DEVELOPMENT MODE: Directly restore session without biometric
        console.log('✅ Restoring development session for user:', storedSession.user?.id);
        authService.currentUser = storedSession.user;
        setIsAuthenticated(true);
      } else {
        console.log('ℹ️ No stored session found');
      }

      setIsLoading(false);
    } catch (error) {
      console.error('❌ Error initializing app:', error);
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary.main} />
      </View>
    );
  }

  return (
    <LanguageProvider>
      <NavigationContainer>
        <Stack.Navigator screenOptions={{ headerShown: false }}>
          {isAuthenticated ? (
            <>
              <Stack.Screen name="MainApp" component={MainNavigator} />
              <Stack.Screen name="FAQ" component={FAQScreen} />
              <Stack.Screen name="AIAssistant" component={AIAssistantScreen} />
            </>
          ) : (
            <>
              <Stack.Screen name="Onboarding" component={OnboardingScreen} />
              <Stack.Screen name="Login" component={LoginScreen} />
              <Stack.Screen name="Register" component={RegisterScreen} />
              <Stack.Screen name="CompleteProfile" component={CompleteProfileScreen} />
              <Stack.Screen name="ForgotPassword" component={ForgotPasswordScreen} />
            </>
          )}
        </Stack.Navigator>
      </NavigationContainer>
    </LanguageProvider>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.neutral.cream,
  },
});


