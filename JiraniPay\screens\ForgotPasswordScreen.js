import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import authService from '../services/authService';
import CountrySelector from '../components/CountrySelector';

const ForgotPasswordScreen = ({ navigation }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [formattedValue, setFormattedValue] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [step, setStep] = useState('phone'); // 'phone', 'otp', or 'password'
  const [loading, setLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [networkProvider, setNetworkProvider] = useState('');
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [selectedCountryCode, setSelectedCountryCode] = useState('UG'); // Default to Uganda

  useEffect(() => {
    // Timer for resend OTP
    let interval = null;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
    } else if (interval) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  /**
   * Handles phone number input changes with country-specific formatting and network detection
   * @param {string} text - The input text
   */
  const handlePhoneNumberChange = (text) => {
    const countryConfig = authService.getCountryConfig(selectedCountryCode);
    if (!countryConfig) return;

    // Remove any non-digit characters and limit to country-specific length
    const cleaned = text.replace(/\D/g, '').substring(0, countryConfig.phoneLength);
    setPhoneNumber(cleaned);

    // Format with selected country code
    const formatted = authService.formatPhoneNumber(cleaned, selectedCountryCode);
    setFormattedValue(formatted);

    // Detect network provider for the selected country
    if (cleaned.length >= countryConfig.phoneLength - 1) {
      const provider = authService.detectNetworkProvider(cleaned, selectedCountryCode);
      setNetworkProvider(provider);
    } else {
      setNetworkProvider('');
    }
  };

  /**
   * Handles country selection change
   * @param {string} countryCode - Selected country code
   * @param {Object} country - Country configuration object
   */
  const handleCountrySelect = (countryCode, country) => {
    setSelectedCountryCode(countryCode);
    // Clear phone number and provider when country changes
    setPhoneNumber('');
    setFormattedValue('');
    setNetworkProvider('');
  };

  /**
   * Handles OTP input changes for individual digit inputs
   * @param {string} text - The input digit
   * @param {number} index - The input field index
   */
  const handleOtpChange = (text, index) => {
    const newOtp = otpCode.split('');
    newOtp[index] = text;
    setOtpCode(newOtp.join(''));
  };

  /**
   * Validates the new password form
   * @returns {boolean} - Whether the form is valid
   */
  const validatePasswordForm = () => {
    // Password validation
    if (!newPassword || newPassword.length < 8) {
      Alert.alert('Error', 'Password must be at least 8 characters long');
      return false;
    }

    // Password strength validation
    const hasNumber = /\d/.test(newPassword);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(newPassword);
    
    if (!hasNumber || !hasSpecialChar) {
      Alert.alert('Error', 'Password must contain at least one number and one special character');
      return false;
    }

    // Confirm password validation
    if (newPassword !== confirmPassword) {
      Alert.alert('Error', 'Passwords do not match');
      return false;
    }

    return true;
  };

  /**
   * Sends OTP to the provided phone number
   */
  const sendOTP = async () => {
    // Validate phone number for selected country
    const validation = authService.validatePhoneNumber(phoneNumber, selectedCountryCode);
    if (!validation.isValid) {
      Alert.alert('Error', validation.error);
      return;
    }

    setLoading(true);
    try {
      const result = await authService.sendOTP(phoneNumber, selectedCountryCode);

      if (result.success) {
        setStep('otp');
        setResendTimer(60); // 60 seconds countdown
        Alert.alert('Success', 'OTP sent to your phone number');
      } else {
        Alert.alert('Error', result.error || 'Failed to send OTP');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Verifies OTP and proceeds to password reset
   */
  const verifyOTP = async () => {
    if (!otpCode || otpCode.length !== 6) {
      Alert.alert('Error', 'Please enter the complete OTP');
      return;
    }

    setLoading(true);
    try {
      const result = await authService.verifyOTP(phoneNumber, otpCode, selectedCountryCode);
      
      if (result.success) {
        setStep('password');
      } else {
        Alert.alert('Error', result.error || 'Invalid OTP');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Resets the password with the new password
   */
  const resetPassword = async () => {
    if (!validatePasswordForm()) return;

    setLoading(true);
    try {
      const result = await authService.forgotPassword(phoneNumber, newPassword, selectedCountryCode);
      
      if (result.success) {
        Alert.alert(
          'Success!',
          'Your password has been reset successfully',
          [
            {
              text: 'Continue',
              onPress: () => navigation.goBack()
            }
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Failed to reset password');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Resends OTP to the phone number
   */
  const resendOTP = async () => {
    if (resendTimer > 0) return;
    
    setLoading(true);
    try {
      const result = await authService.sendOTP(phoneNumber, selectedCountryCode);
      
      if (result.success) {
        setResendTimer(60);
        Alert.alert('Success', 'OTP resent to your phone number');
      } else {
        Alert.alert('Error', result.error || 'Failed to resend OTP');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderPhoneStep = () => (
    <View style={styles.stepContainer}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <Ionicons name="arrow-back" size={24} color="#333" />
      </TouchableOpacity>

      <Text style={styles.title}>Forgot Password</Text>
      <Text style={styles.subtitle}>Enter your phone number to reset your password</Text>

      {/* Country Selector */}
      <CountrySelector
        selectedCountryCode={selectedCountryCode}
        onCountrySelect={handleCountrySelect}
      />

      <View style={styles.phoneInputContainer}>
        <View style={styles.phoneContainer}>
          <Text style={styles.countryCode}>
            {authService.getCountryConfig(selectedCountryCode)?.code || '+256'}
          </Text>
          <TextInput
            style={styles.phoneTextInput}
            value={phoneNumber}
            onChangeText={handlePhoneNumberChange}
            placeholder={`Enter phone number (e.g., ${authService.getCountryConfig(selectedCountryCode)?.example || '777123456'})`}
            keyboardType="phone-pad"
            maxLength={authService.getCountryConfig(selectedCountryCode)?.phoneLength || 10}
          />
        </View>

        {networkProvider && (
          <View style={styles.providerContainer}>
            <Text style={styles.providerText}>
              Network: <Text style={styles.providerName}>{networkProvider}</Text>
            </Text>
          </View>
        )}
      </View>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={sendOTP}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Send OTP</Text>
        )}
      </TouchableOpacity>
    </View>
  );

  const renderOTPStep = () => (
    <View style={styles.stepContainer}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setStep('phone')}
      >
        <Ionicons name="arrow-back" size={24} color="#333" />
      </TouchableOpacity>

      <Text style={styles.title}>Verify OTP</Text>
      <Text style={styles.subtitle}>
        Enter the 6-digit code sent to {formattedValue || '+256' + phoneNumber}
      </Text>

      <View style={styles.otpContainer}>
        {[0, 1, 2, 3, 4, 5].map((index) => (
          <TextInput
            key={index}
            style={styles.otpInput}
            value={otpCode[index] || ''}
            onChangeText={(text) => handleOtpChange(text, index)}
            keyboardType="numeric"
            maxLength={1}
            textAlign="center"
          />
        ))}
      </View>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={verifyOTP}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Verify OTP</Text>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.resendButton, resendTimer > 0 && styles.resendButtonDisabled]}
        onPress={resendOTP}
        disabled={resendTimer > 0}
      >
        <Text style={[styles.resendText, resendTimer > 0 && styles.resendTextDisabled]}>
          {resendTimer > 0 ? `Resend OTP in ${resendTimer}s` : 'Resend OTP'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderPasswordStep = () => (
    <View style={styles.stepContainer}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setStep('otp')}
      >
        <Ionicons name="arrow-back" size={24} color="#333" />
      </TouchableOpacity>

      <Text style={styles.title}>Create New Password</Text>
      <Text style={styles.subtitle}>Enter your new password</Text>

      <View style={styles.inputContainer}>
        <View style={styles.passwordInputContainer}>
          <TextInput
            style={styles.passwordInput}
            value={newPassword}
            onChangeText={setNewPassword}
            placeholder="New Password"
            secureTextEntry={!showNewPassword}
            autoCapitalize="none"
          />
          <TouchableOpacity
            style={styles.eyeButton}
            onPress={() => setShowNewPassword(!showNewPassword)}
          >
            <Ionicons
              name={showNewPassword ? "eye-off-outline" : "eye-outline"}
              size={20}
              color="#666"
            />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.inputContainer}>
        <View style={styles.passwordInputContainer}>
          <TextInput
            style={styles.passwordInput}
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            placeholder="Confirm New Password"
            secureTextEntry={!showConfirmPassword}
            autoCapitalize="none"
          />
          <TouchableOpacity
            style={styles.eyeButton}
            onPress={() => setShowConfirmPassword(!showConfirmPassword)}
          >
            <Ionicons
              name={showConfirmPassword ? "eye-off-outline" : "eye-outline"}
              size={20}
              color="#666"
            />
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.passwordRequirements}>
        <Text style={styles.requirementsTitle}>Password Requirements:</Text>
        <Text style={[styles.requirement, newPassword.length >= 8 && styles.requirementMet]}>
          • At least 8 characters
        </Text>
        <Text style={[styles.requirement, /\d/.test(newPassword) && styles.requirementMet]}>
          • Contains at least one number
        </Text>
        <Text style={[styles.requirement, /[!@#$%^&*(),.?":{}|<>]/.test(newPassword) && styles.requirementMet]}>
          • Contains at least one special character
        </Text>
        <Text style={[styles.requirement, newPassword === confirmPassword && newPassword.length > 0 && styles.requirementMet]}>
          • Passwords match
        </Text>
      </View>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={resetPassword}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Reset Password</Text>
        )}
      </TouchableOpacity>
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {step === 'phone' ? renderPhoneStep() :
         step === 'otp' ? renderOTPStep() : renderPasswordStep()}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  backButton: {
    position: 'absolute',
    top: -50,
    left: 0,
    padding: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 22,
  },
  phoneInputContainer: {
    marginBottom: 30,
  },
  phoneContainer: {
    width: '100%',
    height: 60,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    backgroundColor: '#F8F9FA',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  countryCode: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
    marginRight: 10,
  },
  phoneTextInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    height: '100%',
  },
  providerContainer: {
    marginTop: 10,
    alignItems: 'center',
  },
  providerText: {
    fontSize: 14,
    color: '#666',
  },
  providerName: {
    fontWeight: 'bold',
    color: '#5B37B7',
  },
  inputContainer: {
    marginBottom: 20,
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
    paddingHorizontal: 16,
    height: 60,
  },
  passwordInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    height: '100%',
  },
  eyeButton: {
    padding: 4,
  },
  passwordRequirements: {
    backgroundColor: '#F8F9FA',
    borderRadius: 8,
    padding: 12,
    marginBottom: 20,
  },
  requirementsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  requirement: {
    fontSize: 12,
    color: '#999',
    marginBottom: 4,
  },
  requirementMet: {
    color: '#4CAF50',
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 30,
  },
  otpInput: {
    width: 45,
    height: 55,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
    color: '#333',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  button: {
    backgroundColor: '#5B37B7',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  resendButton: {
    alignItems: 'center',
    padding: 10,
  },
  resendButtonDisabled: {
    opacity: 0.5,
  },
  resendText: {
    fontSize: 16,
    color: '#5B37B7',
    fontWeight: '600',
  },
  resendTextDisabled: {
    color: '#999',
  },
});

export default ForgotPasswordScreen;
