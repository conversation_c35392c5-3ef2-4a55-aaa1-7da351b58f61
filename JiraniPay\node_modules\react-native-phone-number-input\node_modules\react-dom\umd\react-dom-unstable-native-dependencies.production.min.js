/** @license React v16.13.1
 * react-dom-unstable-native-dependencies.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';(function(h,p){"object"===typeof exports&&"undefined"!==typeof module?module.exports=p(require("react-dom"),require("react")):"function"===typeof define&&define.amd?define(["react-dom","react"],p):(h=h||self,h.ReactDOMUnstableNativeDependencies=p(h.ReactDOM,h.React))})(this,function(h,p){function x(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+
b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function M(a){var b=a._dispatchListeners,c=a._dispatchInstances;if(Array.isArray(b))throw Error(x(103));a.currentTarget=b?N(c):null;b=b?b(a):null;a.currentTarget=null;a._dispatchListeners=null;a._dispatchInstances=null;return b}function r(a){do a=a.return;while(a&&5!==a.tag);return a?a:null}function O(a,b,c){for(var f=[];a;)f.push(a),a=r(a);for(a=f.length;0<a--;)b(f[a],"captured",c);
for(a=0;a<f.length;a++)b(f[a],"bubbled",c)}function P(a,b){var c=a.stateNode;if(!c)return null;var f=Q(c);if(!f)return null;c=f[b];a:switch(b){case "onClick":case "onClickCapture":case "onDoubleClick":case "onDoubleClickCapture":case "onMouseDown":case "onMouseDownCapture":case "onMouseMove":case "onMouseMoveCapture":case "onMouseUp":case "onMouseUpCapture":case "onMouseEnter":(f=!f.disabled)||(a=a.type,f=!("button"===a||"input"===a||"select"===a||"textarea"===a));a=!f;break a;default:a=!1}if(a)return null;
if(c&&"function"!==typeof c)throw Error(x(231,b,typeof c));return c}function C(a,b){if(null==b)throw Error(x(30));if(null==a)return b;if(Array.isArray(a)){if(Array.isArray(b))return a.push.apply(a,b),a;a.push(b);return a}return Array.isArray(b)?[a].concat(b):[a,b]}function u(a,b,c){Array.isArray(a)?a.forEach(b,c):a&&b.call(c,a)}function R(a,b,c){if(b=P(a,c.dispatchConfig.phasedRegistrationNames[b]))c._dispatchListeners=C(c._dispatchListeners,b),c._dispatchInstances=C(c._dispatchInstances,a)}function W(a){a&&
a.dispatchConfig.phasedRegistrationNames&&O(a._targetInst,R,a)}function X(a){if(a&&a.dispatchConfig.phasedRegistrationNames){var b=a._targetInst;b=b?r(b):null;O(b,R,a)}}function y(a){if(a&&a.dispatchConfig.registrationName){var b=a._targetInst;if(b&&a&&a.dispatchConfig.registrationName){var c=P(b,a.dispatchConfig.registrationName);c&&(a._dispatchListeners=C(a._dispatchListeners,c),a._dispatchInstances=C(a._dispatchInstances,b))}}}function D(){return!0}function E(){return!1}function z(a,b,c,f){this.dispatchConfig=
a;this._targetInst=b;this.nativeEvent=c;a=this.constructor.Interface;for(var d in a)a.hasOwnProperty(d)&&((b=a[d])?this[d]=b(c):"target"===d?this.target=f:this[d]=c[d]);this.isDefaultPrevented=(null!=c.defaultPrevented?c.defaultPrevented:!1===c.returnValue)?D:E;this.isPropagationStopped=E;return this}function Y(a,b,c,f){if(this.eventPool.length){var d=this.eventPool.pop();this.call(d,a,b,c,f);return d}return new this(a,b,c,f)}function Z(a){if(!(a instanceof this))throw Error(x(279));a.destructor();
10>this.eventPool.length&&this.eventPool.push(a)}function S(a){a.eventPool=[];a.getPooled=Y;a.release=Z}function A(a){return"touchstart"===a||"mousedown"===a}function F(a){return"touchmove"===a||"mousemove"===a}function G(a){return"touchend"===a||"touchcancel"===a||"mouseup"===a}function l(a){return a.timeStamp||a.timestamp}function J(a){a=a.identifier;if(null==a)throw Error(x(138));return a}function aa(a){var b=J(a),c=v[b];c?(c.touchActive=!0,c.startPageX=a.pageX,c.startPageY=a.pageY,c.startTimeStamp=
l(a),c.currentPageX=a.pageX,c.currentPageY=a.pageY,c.currentTimeStamp=l(a),c.previousPageX=a.pageX,c.previousPageY=a.pageY,c.previousTimeStamp=l(a)):(c={touchActive:!0,startPageX:a.pageX,startPageY:a.pageY,startTimeStamp:l(a),currentPageX:a.pageX,currentPageY:a.pageY,currentTimeStamp:l(a),previousPageX:a.pageX,previousPageY:a.pageY,previousTimeStamp:l(a)},v[b]=c);q.mostRecentTimeStamp=l(a)}function ba(a){var b=v[J(a)];b&&(b.touchActive=!0,b.previousPageX=b.currentPageX,b.previousPageY=b.currentPageY,
b.previousTimeStamp=b.currentTimeStamp,b.currentPageX=a.pageX,b.currentPageY=a.pageY,b.currentTimeStamp=l(a),q.mostRecentTimeStamp=l(a))}function ca(a){var b=v[J(a)];b&&(b.touchActive=!1,b.previousPageX=b.currentPageX,b.previousPageY=b.currentPageY,b.previousTimeStamp=b.currentTimeStamp,b.currentPageX=a.pageX,b.currentPageY=a.pageY,b.currentTimeStamp=l(a),q.mostRecentTimeStamp=l(a))}function B(a,b){if(null==b)throw Error(x(334));return null==a?b:Array.isArray(a)?a.concat(b):Array.isArray(b)?[a].concat(b):
[a,b]}var Q=null,T=null,N=null,K=p.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.assign;K(z.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():"unknown"!==typeof a.returnValue&&(a.returnValue=!1),this.isDefaultPrevented=D)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():"unknown"!==typeof a.cancelBubble&&(a.cancelBubble=!0),this.isPropagationStopped=D)},persist:function(){this.isPersistent=
D},isPersistent:E,destructor:function(){var a=this.constructor.Interface,b;for(b in a)this[b]=null;this.nativeEvent=this._targetInst=this.dispatchConfig=null;this.isPropagationStopped=this.isDefaultPrevented=E;this._dispatchInstances=this._dispatchListeners=null}});z.Interface={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(a){return a.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null};z.extend=function(a){function b(){return c.apply(this,
arguments)}var c=this,f=function(){};f.prototype=c.prototype;f=new f;K(f,b.prototype);b.prototype=f;b.prototype.constructor=b;b.Interface=K({},c.Interface,a);b.extend=c.extend;S(b);return b};S(z);var w=z.extend({touchHistory:function(a){return null}});p=["touchstart","mousedown"];var U=["touchmove","mousemove"],V=["touchcancel","touchend","mouseup"],v=[],q={touchBank:v,numberActiveTouches:0,indexOfSingleActiveTouch:-1,mostRecentTimeStamp:0},t={recordTouchTrack:function(a,b){if(F(a))b.changedTouches.forEach(ba);
else if(A(a))b.changedTouches.forEach(aa),q.numberActiveTouches=b.touches.length,1===q.numberActiveTouches&&(q.indexOfSingleActiveTouch=b.touches[0].identifier);else if(G(a)&&(b.changedTouches.forEach(ca),q.numberActiveTouches=b.touches.length,1===q.numberActiveTouches))for(a=0;a<v.length;a++)if(b=v[a],null!=b&&b.touchActive){q.indexOfSingleActiveTouch=a;break}},touchHistory:q},k=null,H=0,L=function(a,b){var c=k;k=a;if(null!==I.GlobalResponderHandler)I.GlobalResponderHandler.onChange(c,a,b)},m={startShouldSetResponder:{phasedRegistrationNames:{bubbled:"onStartShouldSetResponder",
captured:"onStartShouldSetResponderCapture"},dependencies:p},scrollShouldSetResponder:{phasedRegistrationNames:{bubbled:"onScrollShouldSetResponder",captured:"onScrollShouldSetResponderCapture"},dependencies:["scroll"]},selectionChangeShouldSetResponder:{phasedRegistrationNames:{bubbled:"onSelectionChangeShouldSetResponder",captured:"onSelectionChangeShouldSetResponderCapture"},dependencies:["selectionchange"]},moveShouldSetResponder:{phasedRegistrationNames:{bubbled:"onMoveShouldSetResponder",captured:"onMoveShouldSetResponderCapture"},
dependencies:U},responderStart:{registrationName:"onResponderStart",dependencies:p},responderMove:{registrationName:"onResponderMove",dependencies:U},responderEnd:{registrationName:"onResponderEnd",dependencies:V},responderRelease:{registrationName:"onResponderRelease",dependencies:V},responderTerminationRequest:{registrationName:"onResponderTerminationRequest",dependencies:[]},responderGrant:{registrationName:"onResponderGrant",dependencies:[]},responderReject:{registrationName:"onResponderReject",
dependencies:[]},responderTerminate:{registrationName:"onResponderTerminate",dependencies:[]}},I={_getResponder:function(){return k},eventTypes:m,extractEvents:function(a,b,c,f,d){if(A(a))H+=1;else if(G(a))if(0<=H)--H;else return null;t.recordTouchTrack(a,c);if(b&&("scroll"===a&&!c.responderIgnoreScroll||0<H&&"selectionchange"===a||A(a)||F(a))){d=A(a)?m.startShouldSetResponder:F(a)?m.moveShouldSetResponder:"selectionchange"===a?m.selectionChangeShouldSetResponder:m.scrollShouldSetResponder;if(k)b:{var e=
k;for(var g=0,h=e;h;h=r(h))g++;h=0;for(var l=b;l;l=r(l))h++;for(;0<g-h;)e=r(e),g--;for(;0<h-g;)b=r(b),h--;for(;g--;){if(e===b||e===b.alternate)break b;e=r(e);b=r(b)}e=null}else e=b;b=e===k;e=w.getPooled(d,e,c,f);e.touchHistory=t.touchHistory;b?u(e,X):u(e,W);b:{d=e._dispatchListeners;b=e._dispatchInstances;if(Array.isArray(d))for(g=0;g<d.length&&!e.isPropagationStopped();g++){if(d[g](e,b[g])){d=b[g];break b}}else if(d&&d(e,b)){d=b;break b}d=null}e._dispatchInstances=null;e._dispatchListeners=null;
e.isPersistent()||e.constructor.release(e);if(d&&d!==k)if(e=w.getPooled(m.responderGrant,d,c,f),e.touchHistory=t.touchHistory,u(e,y),b=!0===M(e),k)if(g=w.getPooled(m.responderTerminationRequest,k,c,f),g.touchHistory=t.touchHistory,u(g,y),h=!g._dispatchListeners||M(g),g.isPersistent()||g.constructor.release(g),h){g=w.getPooled(m.responderTerminate,k,c,f);g.touchHistory=t.touchHistory;u(g,y);var n=B(n,[e,g]);L(d,b)}else d=w.getPooled(m.responderReject,d,c,f),d.touchHistory=t.touchHistory,u(d,y),n=B(n,
d);else n=B(n,e),L(d,b);else n=null}else n=null;d=k&&A(a);e=k&&F(a);b=k&&G(a);if(d=d?m.responderStart:e?m.responderMove:b?m.responderEnd:null)d=w.getPooled(d,k,c,f),d.touchHistory=t.touchHistory,u(d,y),n=B(n,d);d=k&&"touchcancel"===a;if(a=k&&!d&&G(a))a:{if((a=c.touches)&&0!==a.length)for(e=0;e<a.length;e++)if(b=a[e].target,null!==b&&void 0!==b&&0!==b){g=T(b);b:{for(b=k;g;){if(b===g||b===g.alternate){b=!0;break b}g=r(g)}b=!1}if(b){a=!1;break a}}a=!0}if(a=d?m.responderTerminate:a?m.responderRelease:
null)c=w.getPooled(a,k,c,f),c.touchHistory=t.touchHistory,u(c,y),n=B(n,c),L(null);return n},GlobalResponderHandler:null,injection:{injectGlobalResponderHandler:function(a){I.GlobalResponderHandler=a}}};h=h.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Events;p=h[3];(function(a,b,c){Q=a;T=b;N=c})(h[2],h[0],h[1]);return{__proto__:null,ResponderEventPlugin:I,ResponderTouchHistoryStore:t,injectEventPluginsByName:p}});
