/**
 * Environment Configuration for JiraniPay
 * 
 * This file manages environment-specific settings for development, staging, and production.
 * 
 * SETUP INSTRUCTIONS:
 * 1. Create your Supabase project at https://supabase.com
 * 2. Get your Project URL and anon key from Settings → API
 * 3. Configure SMS provider in Authentication → Settings
 * 4. Update the values below with your actual credentials
 */

// Environment detection
const isDevelopment = __DEV__ || process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';

// Base configuration
const config = {
  // App Information
  app: {
    name: 'JiraniPay',
    version: '1.0.0',
    bundleId: 'com.jiranipay.app',
  },

  // Environment flags
  environment: {
    isDevelopment,
    isProduction,
    enableLogging: isDevelopment,
    enableDebugMode: isDevelopment,
  },

  // API Configuration
  api: {
    timeout: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
  },

  // Authentication Configuration
  auth: {
    // OTP Configuration
    otp: {
      length: 6,
      expiryMinutes: 5,
      resendCooldownSeconds: 60,
    },
    
    // Password Requirements
    password: {
      minLength: 8,
      requireNumbers: true,
      requireSpecialChars: true,
      requireUppercase: false, // More user-friendly for East African users
    },

    // Session Configuration
    session: {
      autoRefresh: true,
      persistSession: true,
      sessionTimeoutHours: 24,
    },

    // Biometric Configuration
    biometric: {
      enabled: true,
      promptMessage: 'Authenticate to access your account',
      fallbackToPassword: true,
    },
  },

  // Regional Configuration for East Africa
  regional: {
    defaultCountry: 'UG', // Uganda
    supportedCountries: ['UG', 'KE', 'TZ', 'RW', 'BI', 'SS', 'ET', 'SO', 'DJ', 'ER'],
    defaultCurrency: 'UGX',
    defaultLanguage: 'en',
    supportedLanguages: ['en', 'sw', 'fr'], // English, Swahili, French
  },

  // Feature Flags
  features: {
    biometricAuth: true,
    multiLanguage: true,
    darkMode: true,
    offlineMode: false, // Future feature
    pushNotifications: false, // Future feature
  },
};

// Development Environment
const developmentConfig = {
  ...config,
  supabase: {
    // ✅ REAL SUPABASE CREDENTIALS CONFIGURED
    url: 'https://rridzjvfjvzaumepzmss.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.X5LOTKHzOUXAJcabvPa1WLfl4lj2HZ5aHPG3QoeafFI',

    // Development-specific settings
    enableRealTimeSubscriptions: false,
    enableEdgeFunctions: false,
  },
  
  // Development API endpoints (if using local backend)
  api: {
    ...config.api,
    baseUrl: 'http://localhost:3000', // Local development server
  },
  
  // Development logging
  logging: {
    level: 'debug',
    enableConsoleLogging: true,
    enableRemoteLogging: false,
  },
};

// Production Environment
const productionConfig = {
  ...config,
  supabase: {
    // ✅ REAL SUPABASE CREDENTIALS CONFIGURED
    url: 'https://rridzjvfjvzaumepzmss.supabase.co',
    anonKey: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.X5LOTKHzOUXAJcabvPa1WLfl4lj2HZ5aHPG3QoeafFI',

    // Production-specific settings
    enableRealTimeSubscriptions: true,
    enableEdgeFunctions: true,
  },
  
  // Production API endpoints
  api: {
    ...config.api,
    baseUrl: 'https://api.jiranipay.com', // Your production API
  },
  
  // Production logging
  logging: {
    level: 'error',
    enableConsoleLogging: false,
    enableRemoteLogging: true,
  },
};

// Export the appropriate configuration based on environment
const currentConfig = isDevelopment ? developmentConfig : productionConfig;

// Validation function to ensure all required configurations are set
export const validateConfiguration = () => {
  const errors = [];

  // Validate Supabase configuration
  if (currentConfig.supabase.url === 'YOUR_SUPABASE_PROJECT_URL') {
    errors.push('Supabase URL not configured');
  }

  if (currentConfig.supabase.anonKey === 'YOUR_SUPABASE_ANON_KEY') {
    errors.push('Supabase anon key not configured');
  }

  // Additional validation for real credentials
  if (!currentConfig.supabase.url.startsWith('https://')) {
    errors.push('Invalid Supabase URL format');
  }

  if (!currentConfig.supabase.anonKey.startsWith('eyJ')) {
    errors.push('Invalid Supabase anon key format');
  }

  // Validate required app configuration
  if (!currentConfig.app.name || !currentConfig.app.version) {
    errors.push('App information incomplete');
  }

  if (errors.length > 0) {
    console.error('🚨 CONFIGURATION ERRORS:');
    errors.forEach(error => console.error(`- ${error}`));
    console.error('\n📋 SETUP INSTRUCTIONS:');
    console.error('1. Create a Supabase project at https://supabase.com');
    console.error('2. Go to Settings → API in your Supabase dashboard');
    console.error('3. Copy your Project URL and anon public key');
    console.error('4. Update config/environment.js with your actual credentials');
    console.error('5. Configure SMS provider in Authentication → Settings');
    
    if (isDevelopment) {
      console.warn('⚠️  Running in development mode with invalid configuration');
      return false;
    } else {
      throw new Error('Production environment requires valid configuration');
    }
  }

  return true;
};

// Log current configuration (development only)
if (isDevelopment && currentConfig.environment.enableLogging) {
  console.log('🔧 JiraniPay Configuration Loaded:');
  console.log(`- Environment: ${isDevelopment ? 'Development' : 'Production'}`);
  console.log(`- App Version: ${currentConfig.app.version}`);
  console.log(`- Default Country: ${currentConfig.regional.defaultCountry}`);
  console.log(`- Supported Languages: ${currentConfig.regional.supportedLanguages.join(', ')}`);
  
  // Validate configuration on startup
  validateConfiguration();
}

export default currentConfig;
