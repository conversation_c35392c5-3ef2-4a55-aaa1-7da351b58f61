# 🔐 Authentication System Test Guide

## Quick Test Credentials

### Password Login
- **Phone**: `777123456`
- **Password**: `password123`
- **Country**: Uganda (UG)

### OTP Login  
- **Phone**: `777123456`
- **OTP**: `123456` (always works in development)
- **Country**: Uganda (UG)

## Test Steps

### 1. Password Authentication Test
1. Open JiraniPay app
2. Select "Password Login" tab
3. Select "Uganda" as country
4. Enter phone: `777123456`
5. Enter password: `password123`
6. Click "Login"
7. **Expected**: Should navigate to dashboard

### 2. OTP Authentication Test
1. Open JiraniPay app
2. Select "OTP Login" tab
3. Select "Uganda" as country
4. Enter phone: `777123456`
5. Click "Send OTP"
6. Enter OTP: `123456`
7. Click "Verify OTP & Login"
8. **Expected**: Should navigate to dashboard

## Console Logs to Look For

### Successful Password Login:
```
🚀 Initializing JiraniPay app...
🔧 Initializing auth service...
🔑 Attempting password login for: 777123456
✅ Development password login successful
🔐 Auth state changed, user: authenticated
✅ Password login successful, navigating to MainApp
```

### Successful OTP Login:
```
📱 Sending OTP to: +256777123456
✅ Development OTP ready (123456)
🔐 Verifying OTP for: +256777123456
✅ Development OTP verified successfully
🔐 Auth state changed, user: authenticated
✅ OTP verified successfully, navigating to MainApp
```

## Troubleshooting

### If Login Fails:
1. Check console for error messages
2. Ensure phone number is valid format
3. Try clearing app data and restarting
4. Verify Expo development server is running

### If Navigation Doesn't Work:
1. Check that MainNavigator is properly configured
2. Verify authentication state is being set correctly
3. Look for navigation errors in console

### If App Won't Start:
1. Run `npm install` to ensure dependencies
2. Clear Metro cache: `npx expo start --clear`
3. Check for syntax errors in code

## Development Notes

- All authentication is mocked in development mode
- Real Supabase integration is commented out
- Sessions are stored locally for testing
- Biometric authentication requires device support

## Next Steps

Once authentication is working:
1. Test dashboard functionality
2. Verify wallet operations
3. Test quick actions
4. Check promotional banners display
