# JiraniPay - AI-Powered Bill Payment App

A modern mobile application for the East African market that allows users to pay bills, buy airtime, manage finances, and get AI-powered financial insights.

## Features

### 🔐 Modern Authentication System
- **Phone Number + OTP Verification**: Secure login using mobile number and SMS OTP
- **Biometric Authentication**: Face ID and Fingerprint support for quick access
- **Network Provider Detection**: Automatically detects MTN, Airtel, or UTL networks
- **Secure Session Management**: JWT-based authentication with encrypted storage

### 💰 Payment & Financial Services
- Pay utility bills (electricity, water, internet)
- Buy airtime and data bundles
- Link multiple bank accounts
- Mobile money integration (MTN, Airtel)
- In-app wallet system
- QR code payments
- Expense tracking and budgeting
- Loan applications and management
- Savings goals

### 🤖 AI Integration (Planned)
- AI-powered budgeting and expense tracking
- Smart transaction categorization
- Financial advice and insights
- Fraud detection
- Loan and savings recommendations

## Tech Stack

- **Frontend**: Expo React Native
- **Backend**: Supabase (PostgreSQL + Auth + Real-time)
- **Authentication**: Supabase Auth with SMS OTP
- **Database**: PostgreSQL with Row Level Security
- **Storage**: Expo SecureStore for sensitive data
- **Biometrics**: Expo LocalAuthentication

## Prerequisites

- Node.js (v16 or higher)
- Expo CLI (`npm install -g expo-cli`)
- Supabase account
- Android Studio / Xcode for testing

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
cd JiraniPay
npm install
```

### 2. Supabase Setup

1. Create a new project at [supabase.com](https://supabase.com)
2. Go to Settings > API to get your URL and anon key
3. Update `services/supabaseClient.js` with your credentials:

```javascript
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
```

### 3. Database Setup

1. Go to your Supabase dashboard
2. Navigate to SQL Editor
3. Copy and paste the contents of `database/schema.sql`
4. Run the SQL to create all necessary tables and policies

### 4. Enable SMS Authentication

1. In your Supabase dashboard, go to Authentication > Settings
2. Enable Phone authentication
3. Configure your SMS provider (Twilio recommended for Uganda)
4. Add your SMS templates

### 5. Configure Phone Authentication for Uganda

In Supabase Auth settings, make sure to:
- Enable Phone authentication
- Set up SMS provider with Uganda (+256) support
- Configure rate limiting appropriately

### 6. Run the App

```bash
# Start the development server
expo start

# Run on Android
expo start --android

# Run on iOS
expo start --ios
```

## Project Structure

```
JiraniPay/
├── App.js                 # Main app component with navigation
├── screens/               # All screen components
│   ├── OnboardingScreen.js
│   ├── LoginScreen.js
│   ├── RegisterScreen.js
│   └── CompleteProfileScreen.js
├── services/              # Business logic and API calls
│   ├── supabaseClient.js  # Supabase configuration
│   └── authService.js     # Authentication service
├── database/              # Database schema and migrations
│   └── schema.sql
└── assets/               # Images and other assets
```

## Authentication Flow

1. **Onboarding**: User sees app introduction slides
2. **Phone Input**: User enters phone number (auto-detects network provider)
3. **OTP Verification**: SMS OTP sent and verified
4. **Profile Completion**: New users complete their profile
5. **Biometric Setup**: Optional biometric authentication setup
6. **Main App**: Access to all app features

## Key Features Implemented

### ✅ Authentication System
- [x] Phone number input with country code
- [x] Network provider detection (MTN, Airtel, UTL)
- [x] OTP verification via SMS
- [x] Biometric authentication setup
- [x] Secure session management
- [x] User profile creation

### 🚧 In Progress
- [ ] Main dashboard
- [ ] Bill payment interface
- [ ] Wallet management
- [ ] Bank account linking
- [ ] Mobile money integration

### 📋 Planned
- [ ] QR code payments
- [ ] AI-powered insights
- [ ] Loan applications
- [ ] Savings goals
- [ ] Transaction history
- [ ] Push notifications

## Security Features

- **Row Level Security (RLS)**: Database-level security ensuring users only access their data
- **JWT Authentication**: Secure token-based authentication
- **Encrypted Storage**: Sensitive data stored using Expo SecureStore
- **Biometric Protection**: Optional biometric authentication
- **Phone Verification**: SMS-based phone number verification

## Uganda-Specific Features

- **Network Detection**: Automatically detects MTN, Airtel, and UTL networks
- **Local Phone Formats**: Handles Uganda phone number formats (+256, 07xx, etc.)
- **Currency Support**: UGX (Ugandan Shilling) as default currency
- **Local Payment Methods**: Integration with local mobile money providers

## Development Notes

### Phone Number Handling
The app automatically formats phone numbers for Uganda:
- Converts local format (07xxxxxxxx) to international (+256xxxxxxxx)
- Detects network provider based on prefix
- Validates phone number format

### Biometric Authentication
- Checks device capability before enabling
- Graceful fallback to PIN/password
- Secure storage of biometric preferences

### Database Design
- Normalized schema with proper relationships
- Row Level Security for data protection
- Automatic timestamp updates
- Indexed for performance

## Testing

### Test Phone Numbers (Development)
When testing with Supabase, you can use test phone numbers:
- Use your actual phone number for real SMS testing
- Configure test mode in Supabase for development

### Biometric Testing
- Test on physical devices (biometrics don't work in simulators)
- Test both fingerprint and face recognition
- Test fallback scenarios

## Troubleshooting

### Common Issues

1. **OTP not received**: Check SMS provider configuration in Supabase
2. **Biometric not working**: Ensure testing on physical device with biometrics set up
3. **Database errors**: Verify RLS policies are correctly configured
4. **Network detection issues**: Check phone number format and prefix mappings

### Debug Mode
Enable debug logging by setting `console.log` statements in services for troubleshooting.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Create an issue in the repository
- Check the troubleshooting section
- Review Supabase documentation for backend issues

---

**Note**: This is a development version. Ensure proper security audits and compliance checks before production deployment.
