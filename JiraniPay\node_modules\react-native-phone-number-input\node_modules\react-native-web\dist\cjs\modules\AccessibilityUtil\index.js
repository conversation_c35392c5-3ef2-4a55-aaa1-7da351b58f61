'use strict';

exports.__esModule = true;

var _buttonLikeRoles = require('./buttonLikeRoles');

var _buttonLikeRoles2 = _interopRequireDefault(_buttonLikeRoles);

var _isDisabled = require('./isDisabled');

var _isDisabled2 = _interopRequireDefault(_isDisabled);

var _propsToAccessibilityComponent = require('./propsToAccessibilityComponent');

var _propsToAccessibilityComponent2 = _interopRequireDefault(_propsToAccessibilityComponent);

var _propsToAriaRole = require('./propsToAriaRole');

var _propsToAriaRole2 = _interopRequireDefault(_propsToAriaRole);

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

/**
 * Copyright (c) 2017-present, <PERSON>.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * 
 */

var AccessibilityUtil = {
  buttonLikeRoles: _buttonLikeRoles2.default,
  isDisabled: _isDisabled2.default,
  propsToAccessibilityComponent: _propsToAccessibilityComponent2.default,
  propsToAriaRole: _propsToAriaRole2.default
};

exports.default = AccessibilityUtil;
module.exports = exports['default'];