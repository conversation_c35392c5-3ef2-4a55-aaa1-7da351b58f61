{"name": "<PERSON><PERSON><PERSON><PERSON>", "license": "0BSD", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-native-community/datetimepicker": "^8.4.1", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@supabase/supabase-js": "^2.50.0", "expo": "~53.0.10", "expo-local-authentication": "~16.0.4", "expo-secure-store": "~14.2.3", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.3", "react-native-gesture-handler": "~2.24.0", "react-native-otp-inputs": "^7.4.0", "react-native-phone-number-input": "^2.1.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}