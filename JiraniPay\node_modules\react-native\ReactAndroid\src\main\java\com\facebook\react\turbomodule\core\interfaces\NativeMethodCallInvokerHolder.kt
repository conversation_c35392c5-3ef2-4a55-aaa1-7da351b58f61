/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

package com.facebook.react.turbomodule.core.interfaces
/**
 * This interface represents the opaque Java object that contains a pointer to and instance of
 * NativeMethodCallInvoker.
 */
public interface NativeMethodCallInvokerHolder
