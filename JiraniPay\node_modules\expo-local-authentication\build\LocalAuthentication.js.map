{"version": 3, "file": "LocalAuthentication.js", "sourceRoot": "", "sources": ["../src/LocalAuthentication.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AACxD,OAAO,SAAS,MAAM,WAAW,CAAC;AAElC,OAAO,uBAAuB,MAAM,2BAA2B,CAAC;AAChE,OAAO,EAEL,kBAAkB,EAElB,aAAa,GAGd,MAAM,6BAA6B,CAAC;AAErC,OAAO,EAEL,kBAAkB,EAElB,aAAa,GAGd,CAAC;AAEF,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB;IACpC,IAAI,CAAC,uBAAuB,CAAC,gBAAgB,EAAE,CAAC;QAC9C,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,EAAE,kBAAkB,CAAC,CAAC;IACjF,CAAC;IACD,OAAO,MAAM,uBAAuB,CAAC,gBAAgB,EAAE,CAAC;AAC1D,CAAC;AAED,cAAc;AACd;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,iCAAiC;IACrD,IAAI,CAAC,uBAAuB,CAAC,iCAAiC,EAAE,CAAC;QAC/D,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,EAAE,mCAAmC,CAAC,CAAC;IAClG,CAAC;IACD,OAAO,MAAM,uBAAuB,CAAC,iCAAiC,EAAE,CAAC;AAC3E,CAAC;AAED,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe;IACnC,IAAI,CAAC,uBAAuB,CAAC,eAAe,EAAE,CAAC;QAC7C,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,EAAE,iBAAiB,CAAC,CAAC;IAChF,CAAC;IACD,OAAO,MAAM,uBAAuB,CAAC,eAAe,EAAE,CAAC;AACzD,CAAC;AAED,cAAc;AACd;;;;;;GAMG;AACH,MAAM,CAAC,KAAK,UAAU,qBAAqB;IACzC,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,EAAE,CAAC;QACnD,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,EAAE,uBAAuB,CAAC,CAAC;IACtF,CAAC;IACD,OAAO,MAAM,uBAAuB,CAAC,qBAAqB,EAAE,CAAC;AAC/D,CAAC;AAED,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACrC,UAAsC,EAAE;IAExC,IAAI,CAAC,uBAAuB,CAAC,iBAAiB,EAAE,CAAC;QAC/C,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,EAAE,mBAAmB,CAAC,CAAC;IAClF,CAAC;IAED,IAAI,OAAO,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,CAAC;QAC5C,SAAS,CACP,OAAO,OAAO,CAAC,aAAa,KAAK,QAAQ,IAAI,OAAO,CAAC,aAAa,CAAC,MAAM,EACzE,6FAA6F,CAC9F,CAAC;IACJ,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,aAAa,IAAI,cAAc,CAAC;IAC9D,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,IAAI,QAAQ,CAAC;IACpD,MAAM,MAAM,GAAG,MAAM,uBAAuB,CAAC,iBAAiB,CAAC;QAC7D,GAAG,OAAO;QACV,aAAa;QACb,WAAW;KACZ,CAAC,CAAC;IAEH,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,cAAc;AACd;;;GAGG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB;IACtC,IAAI,CAAC,uBAAuB,CAAC,kBAAkB,EAAE,CAAC;QAChD,MAAM,IAAI,mBAAmB,CAAC,2BAA2B,EAAE,oBAAoB,CAAC,CAAC;IACnF,CAAC;IACD,MAAM,uBAAuB,CAAC,kBAAkB,EAAE,CAAC;AACrD,CAAC", "sourcesContent": ["import { UnavailabilityError } from 'expo-modules-core';\nimport invariant from 'invariant';\n\nimport ExpoLocalAuthentication from './ExpoLocalAuthentication';\nimport {\n  LocalAuthenticationOptions,\n  AuthenticationType,\n  LocalAuthenticationResult,\n  SecurityLevel,\n  BiometricsSecurityLevel,\n  LocalAuthenticationError,\n} from './LocalAuthentication.types';\n\nexport {\n  LocalAuthenticationOptions,\n  AuthenticationType,\n  LocalAuthenticationResult,\n  SecurityLevel,\n  BiometricsSecurityLevel,\n  LocalAuthenticationError,\n};\n\n// @needsAudit\n/**\n * Determine whether a face or fingerprint scanner is available on the device.\n * @return Returns a promise which fulfils with a `boolean` value indicating whether a face or\n * fingerprint scanner is available on this device.\n */\nexport async function hasHardwareAsync(): Promise<boolean> {\n  if (!ExpoLocalAuthentication.hasHardwareAsync) {\n    throw new UnavailabilityError('expo-local-authentication', 'hasHardwareAsync');\n  }\n  return await ExpoLocalAuthentication.hasHardwareAsync();\n}\n\n// @needsAudit\n/**\n * Determine what kinds of authentications are available on the device.\n * @return Returns a promise which fulfils to an array containing [`AuthenticationType`s](#authenticationtype).\n *\n * Devices can support multiple authentication methods - i.e. `[1,2]` means the device supports both\n * fingerprint and facial recognition. If none are supported, this method returns an empty array.\n */\nexport async function supportedAuthenticationTypesAsync(): Promise<AuthenticationType[]> {\n  if (!ExpoLocalAuthentication.supportedAuthenticationTypesAsync) {\n    throw new UnavailabilityError('expo-local-authentication', 'supportedAuthenticationTypesAsync');\n  }\n  return await ExpoLocalAuthentication.supportedAuthenticationTypesAsync();\n}\n\n// @needsAudit\n/**\n * Determine whether the device has saved fingerprints or facial data to use for authentication.\n * @return Returns a promise which fulfils to `boolean` value indicating whether the device has\n * saved fingerprints or facial data for authentication.\n */\nexport async function isEnrolledAsync(): Promise<boolean> {\n  if (!ExpoLocalAuthentication.isEnrolledAsync) {\n    throw new UnavailabilityError('expo-local-authentication', 'isEnrolledAsync');\n  }\n  return await ExpoLocalAuthentication.isEnrolledAsync();\n}\n\n// @needsAudit\n/**\n * Determine what kind of authentication is enrolled on the device.\n * @return Returns a promise which fulfils with [`SecurityLevel`](#securitylevel).\n * > **Note:** On Android devices prior to M, `SECRET` can be returned if only the SIM lock has been\n * enrolled, which is not the method that [`authenticateAsync`](#localauthenticationauthenticateasyncoptions)\n * prompts.\n */\nexport async function getEnrolledLevelAsync(): Promise<SecurityLevel> {\n  if (!ExpoLocalAuthentication.getEnrolledLevelAsync) {\n    throw new UnavailabilityError('expo-local-authentication', 'getEnrolledLevelAsync');\n  }\n  return await ExpoLocalAuthentication.getEnrolledLevelAsync();\n}\n\n// @needsAudit\n/**\n * Attempts to authenticate via Fingerprint/TouchID (or FaceID if available on the device).\n * > **Note:** Apple requires apps which use FaceID to provide a description of why they use this API.\n * If you try to use FaceID on an iPhone with FaceID without providing `infoPlist.NSFaceIDUsageDescription`\n * in `app.json`, the module will authenticate using device passcode. For more information about\n * usage descriptions on iOS, see [permissions guide](/guides/permissions/#ios).\n * @param options\n * @return Returns a promise which fulfils with [`LocalAuthenticationResult`](#localauthenticationresult).\n */\nexport async function authenticateAsync(\n  options: LocalAuthenticationOptions = {}\n): Promise<LocalAuthenticationResult> {\n  if (!ExpoLocalAuthentication.authenticateAsync) {\n    throw new UnavailabilityError('expo-local-authentication', 'authenticateAsync');\n  }\n\n  if (options.hasOwnProperty('promptMessage')) {\n    invariant(\n      typeof options.promptMessage === 'string' && options.promptMessage.length,\n      'LocalAuthentication.authenticateAsync : `options.promptMessage` must be a non-empty string.'\n    );\n  }\n\n  const promptMessage = options.promptMessage || 'Authenticate';\n  const cancelLabel = options.cancelLabel || 'Cancel';\n  const result = await ExpoLocalAuthentication.authenticateAsync({\n    ...options,\n    promptMessage,\n    cancelLabel,\n  });\n\n  return result;\n}\n\n// @needsAudit\n/**\n * Cancels authentication flow.\n * @platform android\n */\nexport async function cancelAuthenticate(): Promise<void> {\n  if (!ExpoLocalAuthentication.cancelAuthenticate) {\n    throw new UnavailabilityError('expo-local-authentication', 'cancelAuthenticate');\n  }\n  await ExpoLocalAuthentication.cancelAuthenticate();\n}\n"]}