{"version": 3, "file": "serializer.js", "sourceRoot": "", "sources": ["../src/serializer.ts"], "names": [], "mappings": ";;;AAAA,mCAQiB;AAEjB,mCAAgC;AAEhC,iCAA6D;AAE7D,MAAa,cAAe,SAAQ,KAAK;CAAG;AAA5C,wCAA4C;AAE5C,SAAgB,aAAa,CAAC,KAAW;IAEvC,OAAO,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;QAEvB,IAAI,kBAAW,CAAC,KAAK,CAAC,EAAE;YACtB,OAAO,kBAAkB,CAAC,KAAK,CAAC,CAAC;SAClC;aAAM;YACL,OAAO,aAAa,CAAC,KAAK,CAAC,CAAC;SAC7B;IAEH,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAEhB,CAAC;AAZD,sCAYC;AAED,SAAgB,mBAAmB,CAAC,KAAiB;IAEnD,OAAO,KAAK,CAAC,IAAI,CACf,KAAK,CAAC,OAAO,EAAE,CAChB,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAErB,IAAI,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;QAC5B,IAAI,KAAK,CAAC,CAAC,CAAC,KAAG,IAAI,EAAE;YACnB,GAAG,IAAI,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;SACtC;aAAM;YACL,GAAG,IAAI,GAAG,CAAC;YACX,IAAI,kBAAW,CAAC,KAAK,CAAC,EAAE;gBACtB,GAAG,IAAI,kBAAkB,CAAC,KAAK,CAAC,CAAC;aAClC;iBAAM;gBACL,GAAG,IAAI,aAAa,CAAC,KAAK,CAAC,CAAC;aAC7B;SACF;QACD,OAAO,GAAG,CAAC;IAEb,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAEhB,CAAC;AArBD,kDAqBC;AAED,SAAgB,aAAa,CAAC,KAAW;IAEvC,OAAO,iBAAiB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;AAErE,CAAC;AAJD,sCAIC;AAED,SAAS,kBAAkB,CAAC,KAAgB;IAE1C,OAAO,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;AAEtG,CAAC;AAGD,SAAS,iBAAiB,CAAC,KAAe;IACxC,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,IAAI,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE;YAC3B,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;SAChC;QACD,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;KAChC;IACD,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC7B,OAAO,eAAe,CAAC,KAAK,CAAC,CAAC;KAC/B;IACD,IAAI,KAAK,YAAY,aAAK,EAAE;QAC1B,OAAO,cAAc,CAAC,KAAK,CAAC,CAAC;KAC9B;IACD,IAAI,KAAK,YAAY,oBAAY,EAAE;QACjC,OAAO,qBAAqB,CAAC,KAAK,CAAC,CAAC;KACrC;IACD,IAAI,OAAO,KAAK,KAAK,SAAS,EAAE;QAC9B,OAAO,gBAAgB,CAAC,KAAK,CAAC,CAAC;KAChC;IACD,MAAM,IAAI,cAAc,CAAC,mCAAmC,OAAO,KAAK,EAAE,CAAC,CAAC;AAC9E,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAa;IAErC,IAAI,KAAK,GAAG,CAAC,eAAmB,IAAI,KAAK,GAAG,eAAmB,EAAE;QAC/D,MAAM,IAAI,cAAc,CAAC,yHAAyH,CAAC,CAAC;KACrJ;IACD,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;AAC1B,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAa;IACrC,MAAM,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,EAAC,EAAE,CAAC,CAAC;IAC/C,MAAM,eAAe,GAAG,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,EAAC,EAAE,CAAC,CAAC,MAAM,CAAC;IAEjE,IAAI,eAAe,GAAG,EAAE,EAAE;QACxB,MAAM,IAAI,cAAc,CAAC,qGAAqG,CAAC,CAAC;KACjI;IACD,OAAO,GAAG,CAAC;AACb,CAAC;AAED,SAAS,eAAe,CAAC,KAAa;IACpC,IAAI,CAAC,cAAO,CAAC,KAAK,CAAC,EAAE;QACnB,MAAM,IAAI,cAAc,CAAC,sCAAsC,CAAC,CAAC;KAClE;IACD,OAAO,IAAI,KAAK,CAAC,OAAO,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC;AAC1D,CAAC;AAED,SAAS,gBAAgB,CAAC,KAAc;IACtC,OAAO,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAC7B,CAAC;AAED,SAAS,qBAAqB,CAAC,KAAmB;IAChD,OAAO,IAAI,KAAK,CAAC,QAAQ,EAAE,GAAG,CAAC;AACjC,CAAC;AAED,SAAS,cAAc,CAAC,KAAY;IAClC,OAAO,KAAK,CAAC,QAAQ,EAAE,CAAC;AAC1B,CAAC;AAED,SAAS,mBAAmB,CAAC,KAAiB;IAE5C,OAAO,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;QAE5C,IAAI,GAAG,GAAG,GAAG,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;QAClC,IAAI,KAAK,KAAG,IAAI,EAAE;YAChB,GAAG,IAAE,GAAG,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;SACrC;QACD,OAAO,GAAG,CAAC;IAEb,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;AAEd,CAAC;AAED,SAAS,YAAY,CAAC,KAAa;IAEjC,IAAI,CAAC,oBAAa,CAAC,KAAK,CAAC,EAAE;QACzB,MAAM,IAAI,cAAc,CAAC,0GAA0G,CAAC,CAAC;KACtI;IACD,OAAO,KAAK,CAAC;AAEf,CAAC"}