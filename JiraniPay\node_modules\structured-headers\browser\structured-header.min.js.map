{"version": 3, "sources": ["webpack://structuredHeader/./dist/index.js", "webpack://structuredHeader/./dist/parser.js", "webpack://structuredHeader/./dist/serializer.js", "webpack://structuredHeader/./dist/token.js", "webpack://structuredHeader/./dist/types.js", "webpack://structuredHeader/./dist/util.js", "webpack://structuredHeader/webpack/bootstrap", "webpack://structuredHeader/webpack/startup"], "names": ["__createBinding", "this", "Object", "create", "o", "m", "k", "k2", "undefined", "defineProperty", "enumerable", "get", "__exportStar", "exports", "p", "prototype", "hasOwnProperty", "call", "value", "Token", "token_1", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "parseItem", "parseList", "parseDictionary", "types_1", "util_1", "input", "<PERSON><PERSON><PERSON>", "Error", "position", "message", "super", "pos", "<PERSON><PERSON>", "dictionary", "Map", "eof", "thisKey", "parse<PERSON>ey", "member", "lookChar", "parseItemOrInnerList", "parseParameters", "set", "skipOWS", "expectChar", "members", "push", "standaloneItem", "result", "parseBareItem", "checkTrail", "parseInnerList", "innerList", "nextChar", "char", "match", "parseIntegerOrDecimal", "parseString", "parseToken", "parseByteSequence", "parseBoolean", "parameters", "key", "type", "sign", "inputNumber", "isDigit", "getChar", "length", "parseInt", "endsWith", "split", "parseFloat", "outputString", "isAscii", "test", "endPos", "indexOf", "b64<PERSON><PERSON><PERSON>", "substring", "ByteSequence", "c", "substr", "default", "isDigitRegex", "serializeItem", "serializeDictionary", "serializeList", "SerializeError", "serializeBareItem", "serializeParameters", "serializeInnerList", "map", "join", "Number", "isInteger", "toString", "serializeInteger", "out", "toFixed", "replace", "serializeDecimal", "v", "serializeString", "serializeToken", "toBase64", "serializeByteSequence", "serializeBoolean", "Array", "from", "serialize<PERSON>ey", "isValidKeyStr", "isInnerList", "entries", "isValidTokenStr", "TypeError", "base64Value", "asciiRe", "tokenRe", "keyRe", "str", "isArray", "__webpack_module_cache__", "__webpack_exports__", "__webpack_require__", "moduleId", "cachedModule", "module", "__webpack_modules__"], "mappings": "yEACA,IAAIA,EAAmBC,MAAQA,KAAKD,kBAAqBE,OAAOC,OAAS,SAAUC,EAAGC,EAAGC,EAAGC,QAC7EC,IAAPD,IAAkBA,EAAKD,GAC3BJ,OAAOO,eAAeL,EAAGG,EAAI,CAAEG,YAAY,EAAMC,IAAK,WAAa,OAAON,EAAEC,OAC3E,SAAUF,EAAGC,EAAGC,EAAGC,QACTC,IAAPD,IAAkBA,EAAKD,GAC3BF,EAAEG,GAAMF,EAAEC,KAEVM,EAAgBX,MAAQA,KAAKW,cAAiB,SAASP,EAAGQ,GAC1D,IAAK,IAAIC,KAAKT,EAAa,YAANS,GAAoBZ,OAAOa,UAAUC,eAAeC,KAAKJ,EAASC,IAAId,EAAgBa,EAASR,EAAGS,IAE3HZ,OAAOO,eAAeI,EAAS,aAAc,CAAEK,OAAO,IACtDL,EAAQM,WAAQ,EAChBP,EAAa,EAAQ,KAAiBC,GACtCD,EAAa,EAAQ,KAAaC,GAClCD,EAAa,EAAQ,KAAYC,GACjC,IAAIO,EAAU,EAAQ,GACtBlB,OAAOO,eAAeI,EAAS,QAAS,CAAEH,YAAY,EAAMC,IAAK,WAAc,OAAOS,EAAQD,U,oBChB9FjB,OAAOO,eAAeI,EAAS,aAAc,CAAEK,OAAO,IACtDL,EAAQQ,WAAaR,EAAQS,UAAYT,EAAQU,UAAYV,EAAQW,qBAAkB,EACvF,MAAMC,EAAU,EAAQ,KAClBL,EAAU,EAAQ,GAClBM,EAAS,EAAQ,KAKvBb,EAAQW,gBAJR,SAAyBG,GAErB,OADe,IAAIC,EAAOD,GACZH,mBAOlBX,EAAQU,UAJR,SAAmBI,GAEf,OADe,IAAIC,EAAOD,GACZJ,aAOlBV,EAAQS,UAJR,SAAmBK,GAEf,OADe,IAAIC,EAAOD,GACZL,aAGlB,MAAMD,UAAmBQ,MACrB,YAAYC,EAAUC,GAClBC,MAAM,gBAAgBD,eAAqBD,MAGnDjB,EAAQQ,WAAaA,EACrB,MAAMO,EACF,YAAYD,GACR1B,KAAK0B,MAAQA,EACb1B,KAAKgC,IAAM,EAEf,kBACIhC,KAAKiC,SACL,MAAMC,EAAa,IAAIC,IACvB,MAAQnC,KAAKoC,OAAO,CAChB,MAAMC,EAAUrC,KAAKsC,WACrB,IAAIC,EAUJ,GATwB,MAApBvC,KAAKwC,YACLxC,KAAKgC,MACLO,EAASvC,KAAKyC,wBAGdF,EAAS,EAAC,EAAMvC,KAAK0C,mBAEzBR,EAAWS,IAAIN,EAASE,GACxBvC,KAAK4C,UACD5C,KAAKoC,MACL,OAAOF,EAKX,GAHAlC,KAAK6C,WAAW,KAChB7C,KAAKgC,MACLhC,KAAK4C,UACD5C,KAAKoC,MACL,MAAM,IAAIhB,EAAWpB,KAAKgC,IAAK,yCAGvC,OAAOE,EAEX,YACIlC,KAAKiC,SACL,MAAMa,EAAU,GAChB,MAAQ9C,KAAKoC,OAAO,CAGhB,GAFAU,EAAQC,KAAK/C,KAAKyC,wBAClBzC,KAAK4C,UACD5C,KAAKoC,MACL,OAAOU,EAKX,GAHA9C,KAAK6C,WAAW,KAChB7C,KAAKgC,MACLhC,KAAK4C,UACD5C,KAAKoC,MACL,MAAM,IAAIhB,EAAWpB,KAAKgC,IAAK,4CAGvC,OAAOc,EAEX,UAAUE,GAAiB,GACnBA,GACAhD,KAAKiC,SACT,MAAMgB,EAAS,CACXjD,KAAKkD,gBACLlD,KAAK0C,mBAIT,OAFIM,GACAhD,KAAKmD,aACFF,EAEX,uBACI,MAAwB,MAApBjD,KAAKwC,WACExC,KAAKoD,iBAGLpD,KAAKqB,WAAU,GAG9B,iBACIrB,KAAK6C,WAAW,KAChB7C,KAAKgC,MACL,MAAMqB,EAAY,GAClB,MAAQrD,KAAKoC,OAAO,CAEhB,GADApC,KAAKiC,SACmB,MAApBjC,KAAKwC,WAEL,OADAxC,KAAKgC,MACE,CACHqB,EACArD,KAAK0C,mBAGbW,EAAUN,KAAK/C,KAAKqB,WAAU,IAC9B,MAAMiC,EAAWtD,KAAKwC,WACtB,GAAiB,MAAbc,GAAiC,MAAbA,EACpB,MAAM,IAAIlC,EAAWpB,KAAKgC,IAAK,gEAGvC,MAAM,IAAIZ,EAAWpB,KAAKgC,IAAK,oCAEnC,gBACI,MAAMuB,EAAOvD,KAAKwC,WAClB,GAAIe,EAAKC,MAAM,WACX,OAAOxD,KAAKyD,wBAEhB,GAAa,MAATF,EACA,OAAOvD,KAAK0D,cAEhB,GAAIH,EAAKC,MAAM,cACX,OAAOxD,KAAK2D,aAEhB,GAAa,MAATJ,EACA,OAAOvD,KAAK4D,oBAEhB,GAAa,MAATL,EACA,OAAOvD,KAAK6D,eAEhB,MAAM,IAAIzC,EAAWpB,KAAKgC,IAAK,oBAEnC,kBACI,MAAM8B,EAAa,IAAI3B,IACvB,MAAQnC,KAAKoC,OAEI,MADApC,KAAKwC,YADF,CAKhBxC,KAAKgC,MACLhC,KAAKiC,SACL,MAAM8B,EAAM/D,KAAKsC,WACjB,IAAIrB,GAAQ,EACY,MAApBjB,KAAKwC,aACLxC,KAAKgC,MACLf,EAAQjB,KAAKkD,iBAEjBY,EAAWnB,IAAIoB,EAAK9C,GAExB,OAAO6C,EAEX,wBACI,IAAIE,EAAO,UACPC,EAAO,EACPC,EAAc,GASlB,GARwB,MAApBlE,KAAKwC,aACLyB,GAAQ,EACRjE,KAAKgC,QAMJmC,EAAQnE,KAAKwC,YACd,MAAM,IAAIpB,EAAWpB,KAAKgC,IAAK,0BAEnC,MAAQhC,KAAKoC,OAAO,CAChB,MAAMmB,EAAOvD,KAAKoE,UAClB,GAAID,EAAQZ,GACRW,GAAeX,MAEd,IAAa,YAATS,GAA+B,MAATT,EAO1B,CAEDvD,KAAKgC,MACL,MATA,GAAIkC,EAAYG,OAAS,GACrB,MAAM,IAAIjD,EAAWpB,KAAKgC,IAAK,mCAEnCkC,GAAe,IACfF,EAAO,UAOX,GAAa,YAATA,GAAsBE,EAAYG,OAAS,GAC3C,MAAM,IAAIjD,EAAWpB,KAAKgC,IAAK,mCAEnC,GAAa,YAATgC,GAAsBE,EAAYG,OAAS,GAC3C,MAAM,IAAIjD,EAAWpB,KAAKgC,IAAK,mCAGvC,GAAa,YAATgC,EACA,OAAOM,SAASJ,EAAa,IAAMD,EAGnC,GAAIC,EAAYK,SAAS,KACrB,MAAM,IAAInD,EAAWpB,KAAKgC,IAAK,kCAEnC,GAAIkC,EAAYM,MAAM,KAAK,GAAGH,OAAS,EACnC,MAAM,IAAIjD,EAAWpB,KAAKgC,IAAK,4DAEnC,OAAOyC,WAAWP,GAAeD,EAGzC,cACI,IAAIS,EAAe,GAGnB,IAFA1E,KAAK6C,WAAW,KAChB7C,KAAKgC,OACGhC,KAAKoC,OAAO,CAChB,MAAMmB,EAAOvD,KAAKoE,UAClB,GAAa,OAATb,EAAe,CACf,GAAIvD,KAAKoC,MACL,MAAM,IAAIhB,EAAWpB,KAAKgC,IAAK,2BAEnC,MAAMsB,EAAWtD,KAAKoE,UACtB,GAAiB,OAAbd,GAAkC,MAAbA,EACrB,MAAM,IAAIlC,EAAWpB,KAAKgC,IAAK,qEAEnC0C,GAAgBpB,MAEf,IAAa,MAATC,EACL,OAAOmB,EAEN,IAAKjD,EAAOkD,QAAQpB,GACrB,MAAM,IAAI3B,MAAM,sCAGhB8C,GAAgBnB,GAGxB,MAAM,IAAInC,EAAWpB,KAAKgC,IAAK,2BAEnC,aAKI,IAAI0C,EAAe,GACnB,MAAQ1E,KAAKoC,OAAO,CAChB,MAAMmB,EAAOvD,KAAKwC,WAClB,IAAK,kCAAkCoC,KAAKrB,GACxC,OAAO,IAAIpC,EAAQD,MAAMwD,GAE7BA,GAAgB1E,KAAKoE,UAEzB,OAAO,IAAIjD,EAAQD,MAAMwD,GAE7B,oBACI1E,KAAK6C,WAAW,KAChB7C,KAAKgC,MACL,MAAM6C,EAAS7E,KAAK0B,MAAMoD,QAAQ,IAAK9E,KAAKgC,KAC5C,IAAgB,IAAZ6C,EACA,MAAM,IAAIzD,EAAWpB,KAAKgC,IAAK,uEAEnC,MAAM+C,EAAa/E,KAAK0B,MAAMsD,UAAUhF,KAAKgC,IAAK6C,GAElD,GADA7E,KAAKgC,KAAO+C,EAAWV,OAAS,GAC3B,oBAAoBO,KAAKG,GAC1B,MAAM,IAAI3D,EAAWpB,KAAKgC,IAAK,uDAEnC,OAAO,IAAIR,EAAQyD,aAAaF,GAEpC,eACI/E,KAAK6C,WAAW,KAChB7C,KAAKgC,MACL,MAAMuB,EAAOvD,KAAKoE,UAClB,GAAa,MAATb,EACA,OAAO,EAEX,GAAa,MAATA,EACA,OAAO,EAEX,MAAM,IAAInC,EAAWpB,KAAKgC,IAAK,iDAEnC,WACI,IAAKhC,KAAKwC,WAAWgB,MAAM,WACvB,MAAM,IAAIpC,EAAWpB,KAAKgC,IAAK,qDAEnC,IAAI0C,EAAe,GACnB,MAAQ1E,KAAKoC,OAAO,CAChB,MAAMmB,EAAOvD,KAAKwC,WAClB,IAAK,kBAAkBoC,KAAKrB,GACxB,OAAOmB,EAEXA,GAAgB1E,KAAKoE,UAEzB,OAAOM,EAKX,WACI,OAAO1E,KAAK0B,MAAM1B,KAAKgC,KAK3B,WAAWuB,GACP,GAAIvD,KAAKwC,aAAee,EACpB,MAAM,IAAInC,EAAWpB,KAAKgC,IAAK,YAAYuB,KAGnD,UACI,OAAOvD,KAAK0B,MAAM1B,KAAKgC,OAE3B,MACI,OAAOhC,KAAKgC,KAAOhC,KAAK0B,MAAM2C,OAGlC,UACI,OAAa,CACT,MAAMa,EAAIlF,KAAK0B,MAAMyD,OAAOnF,KAAKgC,IAAK,GACtC,GAAU,MAANkD,GAAmB,OAANA,EAIb,MAHAlF,KAAKgC,OAQjB,SACI,KAA2B,MAApBhC,KAAKwC,YACRxC,KAAKgC,MAKb,aAEI,GADAhC,KAAKiC,UACAjC,KAAKoC,MACN,MAAM,IAAIhB,EAAWpB,KAAKgC,IAAK,0CAI3CpB,EAAQwE,QAAUzD,EAClB,MAAM0D,EAAe,UACrB,SAASlB,EAAQZ,GACb,OAAO8B,EAAaT,KAAKrB,K,oBCnV7BtD,OAAOO,eAAeI,EAAS,aAAc,CAAEK,OAAO,IACtDL,EAAQ0E,cAAgB1E,EAAQ2E,oBAAsB3E,EAAQ4E,cAAgB5E,EAAQ6E,oBAAiB,EACvG,MAAMjE,EAAU,EAAQ,KAClBL,EAAU,EAAQ,GAClBM,EAAS,EAAQ,KACvB,MAAMgE,UAAuB7D,OAiC7B,SAAS0D,EAAc5D,GACnB,OAAOgE,EAAkBhE,EAAM,IAAMiE,EAAoBjE,EAAM,IAGnE,SAASkE,EAAmBlE,GACxB,MAAO,IAAIA,EAAM,GAAGmE,KAAI5E,GAASqE,EAAcrE,KAAQ6E,KAAK,QAAQH,EAAoBjE,EAAM,MAElG,SAASgE,EAAkBhE,GACvB,GAAqB,iBAAVA,EACP,OAAIqE,OAAOC,UAAUtE,GAmB7B,SAA0BA,GACtB,GAAIA,GAAS,iBAAmBA,EAAQ,gBACpC,MAAM,IAAI+D,EAAe,2HAE7B,OAAO/D,EAAMuE,WAtBEC,CAAiBxE,GAwBpC,SAA0BA,GACtB,MAAMyE,EAAMzE,EAAM0E,QAAQ,GAAGC,QAAQ,MAAO,IAE5C,GADwBF,EAAI3B,MAAM,KAAK,GAAG6B,QAAQ,IAAK,IAAIhC,OACrC,GAClB,MAAM,IAAIoB,EAAe,uGAE7B,OAAOU,EA5BIG,CAAiB5E,GAE5B,GAAqB,iBAAVA,EACP,OA2BR,SAAyBA,GACrB,IAAKD,EAAOkD,QAAQjD,GAChB,MAAM,IAAI+D,EAAe,wCAE7B,MAAO,IAAI/D,EAAM2E,QAAQ,WAAYE,GAAM,KAAOA,OA/BvCC,CAAgB9E,GAE3B,GAAIA,aAAiBP,EAAQD,MACzB,OAoCR,SAAwBQ,GACpB,OAAOA,EAAMuE,WArCFQ,CAAe/E,GAE1B,GAAIA,aAAiBF,EAAQyD,aACzB,OA8BR,SAA+BvD,GAC3B,MAAO,IAAIA,EAAMgF,cA/BNC,CAAsBjF,GAEjC,GAAqB,kBAAVA,EACP,OAwBR,SAA0BA,GACtB,OAAOA,EAAQ,KAAO,KAzBXkF,CAAiBlF,GAE5B,MAAM,IAAI+D,EAAe,0CAA0C/D,GA+BvE,SAASiE,EAAoBjE,GACzB,OAAOmF,MAAMC,KAAKpF,GAAOmE,KAAI,EAAE9B,EAAK9C,MAChC,IAAIkF,EAAM,IAAMY,EAAahD,GAI7B,OAHc,IAAV9C,IACAkF,GAAO,IAAMT,EAAkBzE,IAE5BkF,KACRL,KAAK,IAEZ,SAASiB,EAAarF,GAClB,IAAKD,EAAOuF,cAActF,GACtB,MAAM,IAAI+D,EAAe,4GAE7B,OAAO/D,EArGXd,EAAQ6E,eAAiBA,EAWzB7E,EAAQ4E,cAVR,SAAuB9D,GACnB,OAAOA,EAAMmE,KAAI5E,GACTQ,EAAOwF,YAAYhG,GACZ2E,EAAmB3E,GAGnBqE,EAAcrE,KAE1B6E,KAAK,OAqBZlF,EAAQ2E,oBAlBR,SAA6B7D,GACzB,OAAOmF,MAAMC,KAAKpF,EAAMwF,WAAWrB,KAAI,EAAE9B,EAAK9C,MAC1C,IAAIkF,EAAMY,EAAahD,GAavB,OAZiB,IAAb9C,EAAM,GACNkF,GAAOR,EAAoB1E,EAAM,KAGjCkF,GAAO,IACH1E,EAAOwF,YAAYhG,GACnBkF,GAAOP,EAAmB3E,GAG1BkF,GAAOb,EAAcrE,IAGtBkF,KACRL,KAAK,OAMZlF,EAAQ0E,cAAgBA,G,kBCzCxBrF,OAAOO,eAAeI,EAAS,aAAc,CAAEK,OAAO,IACtDL,EAAQM,WAAQ,EAChB,MAAMO,EAAS,EAAQ,KAYvBb,EAAQM,MAXR,MACI,YAAYD,GACR,IAAKQ,EAAO0F,gBAAgBlG,GACxB,MAAM,IAAImG,UAAU,iJAExBpH,KAAKiB,MAAQA,EAEjB,WACI,OAAOjB,KAAKiB,S,kBCXpBhB,OAAOO,eAAeI,EAAS,aAAc,CAAEK,OAAO,IACtDL,EAAQqE,kBAAe,EASvBrE,EAAQqE,aARR,MACI,YAAYoC,GACRrH,KAAKqH,YAAcA,EAEvB,WACI,OAAOrH,KAAKqH,e,kBCPpBpH,OAAOO,eAAeI,EAAS,aAAc,CAAEK,OAAO,IACtDL,EAAQqG,YAAcrG,EAAQoG,cAAgBpG,EAAQuG,gBAAkBvG,EAAQ+D,aAAU,EAC1F,MAAM2C,EAAU,iBACVC,EAAU,4CACVC,EAAQ,yBAId5G,EAAQ+D,QAHR,SAAiB8C,GACb,OAAOH,EAAQ1C,KAAK6C,IAMxB7G,EAAQuG,gBAHR,SAAyBM,GACrB,OAAOF,EAAQ3C,KAAK6C,IAMxB7G,EAAQoG,cAHR,SAAuBS,GACnB,OAAOD,EAAM5C,KAAK6C,IAMtB7G,EAAQqG,YAHR,SAAqBvF,GACjB,OAAOmF,MAAMa,QAAQhG,EAAM,OClB3BiG,EAA2B,GCE3BC,EDCJ,SAASC,EAAoBC,GAE5B,IAAIC,EAAeJ,EAAyBG,GAC5C,QAAqBvH,IAAjBwH,EACH,OAAOA,EAAanH,QAGrB,IAAIoH,EAASL,EAAyBG,GAAY,CAGjDlH,QAAS,IAOV,OAHAqH,EAAoBH,GAAU9G,KAAKgH,EAAOpH,QAASoH,EAAQA,EAAOpH,QAASiH,GAGpEG,EAAOpH,QClBWiH,CAAoB,K", "file": "structured-header.min.js", "sourcesContent": ["\"use strict\";\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Token = void 0;\n__exportStar(require(\"./serializer\"), exports);\n__exportStar(require(\"./parser\"), exports);\n__exportStar(require(\"./types\"), exports);\nvar token_1 = require(\"./token\");\nObject.defineProperty(exports, \"Token\", { enumerable: true, get: function () { return token_1.Token; } });\n//# sourceMappingURL=index.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ParseError = exports.parseItem = exports.parseList = exports.parseDictionary = void 0;\nconst types_1 = require(\"./types\");\nconst token_1 = require(\"./token\");\nconst util_1 = require(\"./util\");\nfunction parseDictionary(input) {\n    const parser = new Parser(input);\n    return parser.parseDictionary();\n}\nexports.parseDictionary = parseDictionary;\nfunction parseList(input) {\n    const parser = new Parser(input);\n    return parser.parseList();\n}\nexports.parseList = parseList;\nfunction parseItem(input) {\n    const parser = new Parser(input);\n    return parser.parseItem();\n}\nexports.parseItem = parseItem;\nclass ParseError extends Error {\n    constructor(position, message) {\n        super(`Parse error: ${message} at offset ${position}`);\n    }\n}\nexports.ParseError = ParseError;\nclass Parser {\n    constructor(input) {\n        this.input = input;\n        this.pos = 0;\n    }\n    parseDictionary() {\n        this.skipWS();\n        const dictionary = new Map();\n        while (!this.eof()) {\n            const thisKey = this.parseKey();\n            let member;\n            if (this.lookChar() === '=') {\n                this.pos++;\n                member = this.parseItemOrInnerList();\n            }\n            else {\n                member = [true, this.parseParameters()];\n            }\n            dictionary.set(thisKey, member);\n            this.skipOWS();\n            if (this.eof()) {\n                return dictionary;\n            }\n            this.expectChar(',');\n            this.pos++;\n            this.skipOWS();\n            if (this.eof()) {\n                throw new ParseError(this.pos, 'Dictionary contained a trailing comma');\n            }\n        }\n        return dictionary;\n    }\n    parseList() {\n        this.skipWS();\n        const members = [];\n        while (!this.eof()) {\n            members.push(this.parseItemOrInnerList());\n            this.skipOWS();\n            if (this.eof()) {\n                return members;\n            }\n            this.expectChar(',');\n            this.pos++;\n            this.skipOWS();\n            if (this.eof()) {\n                throw new ParseError(this.pos, 'A list may not end with a trailing comma');\n            }\n        }\n        return members;\n    }\n    parseItem(standaloneItem = true) {\n        if (standaloneItem)\n            this.skipWS();\n        const result = [\n            this.parseBareItem(),\n            this.parseParameters()\n        ];\n        if (standaloneItem)\n            this.checkTrail();\n        return result;\n    }\n    parseItemOrInnerList() {\n        if (this.lookChar() === '(') {\n            return this.parseInnerList();\n        }\n        else {\n            return this.parseItem(false);\n        }\n    }\n    parseInnerList() {\n        this.expectChar('(');\n        this.pos++;\n        const innerList = [];\n        while (!this.eof()) {\n            this.skipWS();\n            if (this.lookChar() === ')') {\n                this.pos++;\n                return [\n                    innerList,\n                    this.parseParameters()\n                ];\n            }\n            innerList.push(this.parseItem(false));\n            const nextChar = this.lookChar();\n            if (nextChar !== ' ' && nextChar !== ')') {\n                throw new ParseError(this.pos, 'Expected a whitespace or ) after every item in an inner list');\n            }\n        }\n        throw new ParseError(this.pos, 'Could not find end of inner list');\n    }\n    parseBareItem() {\n        const char = this.lookChar();\n        if (char.match(/^[-0-9]/)) {\n            return this.parseIntegerOrDecimal();\n        }\n        if (char === '\"') {\n            return this.parseString();\n        }\n        if (char.match(/^[A-Za-z*]/)) {\n            return this.parseToken();\n        }\n        if (char === ':') {\n            return this.parseByteSequence();\n        }\n        if (char === '?') {\n            return this.parseBoolean();\n        }\n        throw new ParseError(this.pos, 'Unexpected input');\n    }\n    parseParameters() {\n        const parameters = new Map();\n        while (!this.eof()) {\n            const char = this.lookChar();\n            if (char !== ';') {\n                break;\n            }\n            this.pos++;\n            this.skipWS();\n            const key = this.parseKey();\n            let value = true;\n            if (this.lookChar() === '=') {\n                this.pos++;\n                value = this.parseBareItem();\n            }\n            parameters.set(key, value);\n        }\n        return parameters;\n    }\n    parseIntegerOrDecimal() {\n        let type = 'integer';\n        let sign = 1;\n        let inputNumber = '';\n        if (this.lookChar() === '-') {\n            sign = -1;\n            this.pos++;\n        }\n        // The spec wants this check but it's unreachable code.\n        //if (this.eof()) {\n        //  throw new ParseError(this.pos, 'Empty integer');\n        //}\n        if (!isDigit(this.lookChar())) {\n            throw new ParseError(this.pos, 'Expected a digit (0-9)');\n        }\n        while (!this.eof()) {\n            const char = this.getChar();\n            if (isDigit(char)) {\n                inputNumber += char;\n            }\n            else if (type === 'integer' && char === '.') {\n                if (inputNumber.length > 12) {\n                    throw new ParseError(this.pos, 'Exceeded maximum decimal length');\n                }\n                inputNumber += '.';\n                type = 'decimal';\n            }\n            else {\n                // We need to 'prepend' the character, so it's just a rewind\n                this.pos--;\n                break;\n            }\n            if (type === 'integer' && inputNumber.length > 15) {\n                throw new ParseError(this.pos, 'Exceeded maximum integer length');\n            }\n            if (type === 'decimal' && inputNumber.length > 16) {\n                throw new ParseError(this.pos, 'Exceeded maximum decimal length');\n            }\n        }\n        if (type === 'integer') {\n            return parseInt(inputNumber, 10) * sign;\n        }\n        else {\n            if (inputNumber.endsWith('.')) {\n                throw new ParseError(this.pos, 'Decimal cannot end on a period');\n            }\n            if (inputNumber.split('.')[1].length > 3) {\n                throw new ParseError(this.pos, 'Number of digits after the decimal point cannot exceed 3');\n            }\n            return parseFloat(inputNumber) * sign;\n        }\n    }\n    parseString() {\n        let outputString = '';\n        this.expectChar('\"');\n        this.pos++;\n        while (!this.eof()) {\n            const char = this.getChar();\n            if (char === '\\\\') {\n                if (this.eof()) {\n                    throw new ParseError(this.pos, 'Unexpected end of input');\n                }\n                const nextChar = this.getChar();\n                if (nextChar !== '\\\\' && nextChar !== '\"') {\n                    throw new ParseError(this.pos, 'A backslash must be followed by another backslash or double quote');\n                }\n                outputString += nextChar;\n            }\n            else if (char === '\"') {\n                return outputString;\n            }\n            else if (!util_1.isAscii(char)) {\n                throw new Error('Strings must be in the ASCII range');\n            }\n            else {\n                outputString += char;\n            }\n        }\n        throw new ParseError(this.pos, 'Unexpected end of input');\n    }\n    parseToken() {\n        // The specification wants this check, but it's an unreachable code block.\n        // if (!/^[A-Za-z*]/.test(this.lookChar())) {\n        //  throw new ParseError(this.pos, 'A token must begin with an asterisk or letter (A-Z, a-z)');\n        //}\n        let outputString = '';\n        while (!this.eof()) {\n            const char = this.lookChar();\n            if (!/^[:/!#$%&'*+\\-.^_`|~A-Za-z0-9]$/.test(char)) {\n                return new token_1.Token(outputString);\n            }\n            outputString += this.getChar();\n        }\n        return new token_1.Token(outputString);\n    }\n    parseByteSequence() {\n        this.expectChar(':');\n        this.pos++;\n        const endPos = this.input.indexOf(':', this.pos);\n        if (endPos === -1) {\n            throw new ParseError(this.pos, 'Could not find a closing \":\" character to mark end of Byte Sequence');\n        }\n        const b64Content = this.input.substring(this.pos, endPos);\n        this.pos += b64Content.length + 1;\n        if (!/^[A-Za-z0-9+/=]*$/.test(b64Content)) {\n            throw new ParseError(this.pos, 'ByteSequence does not contain a valid base64 string');\n        }\n        return new types_1.ByteSequence(b64Content);\n    }\n    parseBoolean() {\n        this.expectChar('?');\n        this.pos++;\n        const char = this.getChar();\n        if (char === '1') {\n            return true;\n        }\n        if (char === '0') {\n            return false;\n        }\n        throw new ParseError(this.pos, 'Unexpected character. Expected a \"1\" or a \"0\"');\n    }\n    parseKey() {\n        if (!this.lookChar().match(/^[a-z*]/)) {\n            throw new ParseError(this.pos, 'A key must begin with an asterisk or letter (a-z)');\n        }\n        let outputString = '';\n        while (!this.eof()) {\n            const char = this.lookChar();\n            if (!/^[a-z0-9_\\-.*]$/.test(char)) {\n                return outputString;\n            }\n            outputString += this.getChar();\n        }\n        return outputString;\n    }\n    /**\n     * Looks at the next character without advancing the cursor.\n     */\n    lookChar() {\n        return this.input[this.pos];\n    }\n    /**\n     * Checks if the next character is 'char', and fail otherwise.\n     */\n    expectChar(char) {\n        if (this.lookChar() !== char) {\n            throw new ParseError(this.pos, `Expected ${char}`);\n        }\n    }\n    getChar() {\n        return this.input[this.pos++];\n    }\n    eof() {\n        return this.pos >= this.input.length;\n    }\n    // Advances the pointer to skip all whitespace.\n    skipOWS() {\n        while (true) {\n            const c = this.input.substr(this.pos, 1);\n            if (c === ' ' || c === '\\t') {\n                this.pos++;\n            }\n            else {\n                break;\n            }\n        }\n    }\n    // Advances the pointer to skip all spaces\n    skipWS() {\n        while (this.lookChar() === ' ') {\n            this.pos++;\n        }\n    }\n    // At the end of parsing, we need to make sure there are no bytes after the\n    // header except whitespace.\n    checkTrail() {\n        this.skipWS();\n        if (!this.eof()) {\n            throw new ParseError(this.pos, 'Unexpected characters at end of input');\n        }\n    }\n}\nexports.default = Parser;\nconst isDigitRegex = /^[0-9]$/;\nfunction isDigit(char) {\n    return isDigitRegex.test(char);\n}\n//# sourceMappingURL=parser.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.serializeItem = exports.serializeDictionary = exports.serializeList = exports.SerializeError = void 0;\nconst types_1 = require(\"./types\");\nconst token_1 = require(\"./token\");\nconst util_1 = require(\"./util\");\nclass SerializeError extends Error {\n}\nexports.SerializeError = SerializeError;\nfunction serializeList(input) {\n    return input.map(value => {\n        if (util_1.isInnerList(value)) {\n            return serializeInnerList(value);\n        }\n        else {\n            return serializeItem(value);\n        }\n    }).join(', ');\n}\nexports.serializeList = serializeList;\nfunction serializeDictionary(input) {\n    return Array.from(input.entries()).map(([key, value]) => {\n        let out = serializeKey(key);\n        if (value[0] === true) {\n            out += serializeParameters(value[1]);\n        }\n        else {\n            out += '=';\n            if (util_1.isInnerList(value)) {\n                out += serializeInnerList(value);\n            }\n            else {\n                out += serializeItem(value);\n            }\n        }\n        return out;\n    }).join(', ');\n}\nexports.serializeDictionary = serializeDictionary;\nfunction serializeItem(input) {\n    return serializeBareItem(input[0]) + serializeParameters(input[1]);\n}\nexports.serializeItem = serializeItem;\nfunction serializeInnerList(input) {\n    return `(${input[0].map(value => serializeItem(value)).join(' ')})${serializeParameters(input[1])}`;\n}\nfunction serializeBareItem(input) {\n    if (typeof input === 'number') {\n        if (Number.isInteger(input)) {\n            return serializeInteger(input);\n        }\n        return serializeDecimal(input);\n    }\n    if (typeof input === 'string') {\n        return serializeString(input);\n    }\n    if (input instanceof token_1.Token) {\n        return serializeToken(input);\n    }\n    if (input instanceof types_1.ByteSequence) {\n        return serializeByteSequence(input);\n    }\n    if (typeof input === 'boolean') {\n        return serializeBoolean(input);\n    }\n    throw new SerializeError(`Cannot serialize values of type ${typeof input}`);\n}\nfunction serializeInteger(input) {\n    if (input < -999999999999999 || input > 999999999999999) {\n        throw new SerializeError('Structured headers can only encode integers in the range range of -999,999,999,999,999 to 999,999,999,999,999 inclusive');\n    }\n    return input.toString();\n}\nfunction serializeDecimal(input) {\n    const out = input.toFixed(3).replace(/0+$/, '');\n    const signifantDigits = out.split('.')[0].replace('-', '').length;\n    if (signifantDigits > 12) {\n        throw new SerializeError('Fractional numbers are not allowed to have more than 12 significant digits before the decimal point');\n    }\n    return out;\n}\nfunction serializeString(input) {\n    if (!util_1.isAscii(input)) {\n        throw new SerializeError('Only ASCII strings may be serialized');\n    }\n    return `\"${input.replace(/(\"|\\\\)/g, (v) => '\\\\' + v)}\"`;\n}\nfunction serializeBoolean(input) {\n    return input ? '?1' : '?0';\n}\nfunction serializeByteSequence(input) {\n    return `:${input.toBase64()}:`;\n}\nfunction serializeToken(input) {\n    return input.toString();\n}\nfunction serializeParameters(input) {\n    return Array.from(input).map(([key, value]) => {\n        let out = ';' + serializeKey(key);\n        if (value !== true) {\n            out += '=' + serializeBareItem(value);\n        }\n        return out;\n    }).join('');\n}\nfunction serializeKey(input) {\n    if (!util_1.isValidKeyStr(input)) {\n        throw new SerializeError('Keys in dictionaries must only contain lowercase letter, numbers, _-*. and must start with a letter or *');\n    }\n    return input;\n}\n//# sourceMappingURL=serializer.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.Token = void 0;\nconst util_1 = require(\"./util\");\nclass Token {\n    constructor(value) {\n        if (!util_1.isValidTokenStr(value)) {\n            throw new TypeError('Invalid character in Token string. Tokens must start with *, A-Z and the rest of the string may only contain a-z, A-Z, 0-9, :/!#$%&\\'*+-.^_`|~');\n        }\n        this.value = value;\n    }\n    toString() {\n        return this.value;\n    }\n}\nexports.Token = Token;\n//# sourceMappingURL=token.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.ByteSequence = void 0;\nclass ByteSequence {\n    constructor(base64Value) {\n        this.base64Value = base64Value;\n    }\n    toBase64() {\n        return this.base64Value;\n    }\n}\nexports.ByteSequence = ByteSequence;\n//# sourceMappingURL=types.js.map", "\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nexports.isInnerList = exports.isValidKeyStr = exports.isValidTokenStr = exports.isAscii = void 0;\nconst asciiRe = /^[\\x20-\\x7E]*$/;\nconst tokenRe = /^[a-zA-Z*][:/!#$%&'*+\\-.^_`|~A-Za-z0-9]*$/;\nconst keyRe = /^[a-z*][*\\-_.a-z0-9]*$/;\nfunction isAscii(str) {\n    return asciiRe.test(str);\n}\nexports.isAscii = isAscii;\nfunction isValidTokenStr(str) {\n    return tokenRe.test(str);\n}\nexports.isValidTokenStr = isValidTokenStr;\nfunction isValidKeyStr(str) {\n    return keyRe.test(str);\n}\nexports.isValidKeyStr = isValidKeyStr;\nfunction isInnerList(input) {\n    return Array.isArray(input[0]);\n}\nexports.isInnerList = isInnerList;\n//# sourceMappingURL=util.js.map", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(590);\n"], "sourceRoot": ""}