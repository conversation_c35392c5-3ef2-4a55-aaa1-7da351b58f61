/** @license React v0.19.1
 * scheduler-unstable_mock.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';(function(b,w){"object"===typeof exports&&"undefined"!==typeof module?w(exports):"function"===typeof define&&define.amd?define(["exports"],w):(b=b||self,w(b.SchedulerMock={}))})(this,function(b){function w(){return-1!==y&&null!==k&&k.length>=y||D&&E?t=!0:!1}function J(){if(h)throw Error("Already flushing work.");if(null!==f){var a=f;h=!0;try{var c=!0;do c=a(!0,g);while(c);c||(f=null);return!0}finally{h=!1}}else return!1}function F(a,c){var G=a.length;a.push(c);a:for(;;){var b=G-1>>>1,
p=a[b];if(void 0!==p&&0<z(p,c))a[b]=c,a[G]=p,G=b;else break a}}function l(a){a=a[0];return void 0===a?null:a}function A(a){var c=a[0];if(void 0!==c){var b=a.pop();if(b!==c){a[0]=b;a:for(var n=0,p=a.length;n<p;){var e=2*(n+1)-1,f=a[e],d=e+1,g=a[d];if(void 0!==f&&0>z(f,b))void 0!==g&&0>z(g,f)?(a[n]=g,a[d]=b,n=d):(a[n]=f,a[e]=b,n=e);else if(void 0!==g&&0>z(g,b))a[n]=g,a[d]=b,n=d;else break a}}return c}return null}function z(a,c){var b=a.sortIndex-c.sortIndex;return 0!==b?b:a.id-c.id}function B(a){for(var c=
l(q);null!==c;){if(null===c.callback)A(q);else if(c.startTime<=a)A(q),c.sortIndex=c.expirationTime,F(m,c);else break;c=l(q)}}function H(a){x=!1;B(a);if(!u)if(null!==l(m))u=!0,f=I;else{var c=l(q);null!==c&&(a=c.startTime-a,r=H,v=g+a)}}function I(a,c){u=!1;x&&(x=!1,r=null,v=-1);C=!0;var b=e;try{B(c);for(d=l(m);null!==d&&(!(d.expirationTime>c)||a&&!w());){var f=d.callback;if(null!==f){d.callback=null;e=d.priorityLevel;var p=f(d.expirationTime<=c);c=g;"function"===typeof p?d.callback=p:d===l(m)&&A(m);
B(c)}else A(m);d=l(m)}if(null!==d)var h=!0;else{var k=l(q);if(null!==k){var t=k.startTime-c;r=H;v=g+t}h=!1}return h}finally{d=null,e=b,C=!1}}function K(a){switch(a){case 1:return-1;case 2:return 250;case 5:return 1073741823;case 4:return 1E4;default:return 5E3}}var g=0,f=null,r=null,v=-1,k=null,y=-1,t=!1,h=!1,E=!1,D=!1,m=[],q=[],L=1,d=null,e=3,C=!1,u=!1,x=!1;b.unstable_IdlePriority=5;b.unstable_ImmediatePriority=1;b.unstable_LowPriority=4;b.unstable_NormalPriority=3;b.unstable_Profiling=null;b.unstable_UserBlockingPriority=
2;b.unstable_advanceTime=function(a){g+=a;null!==r&&v<=g&&(r(g),v=-1,r=null)};b.unstable_cancelCallback=function(a){a.callback=null};b.unstable_clearYields=function(){if(null===k)return[];var a=k;k=null;return a};b.unstable_continueExecution=function(){u||C||(u=!0,f=I)};b.unstable_flushAll=function(){if(null!==k)throw Error("Log is not empty. Assert on the log of yielded values before flushing additional work.");J();if(null!==k)throw Error("While flushing work, something yielded a value. Use an assertion helper to assert on the log of yielded values, e.g. expect(Scheduler).toFlushAndYield([...])");
};b.unstable_flushAllWithoutAsserting=J;b.unstable_flushExpired=function(){if(h)throw Error("Already flushing work.");if(null!==f){h=!0;try{f(!1,g)||(f=null)}finally{h=!1}}};b.unstable_flushNumberOfYields=function(a){if(h)throw Error("Already flushing work.");if(null!==f){var c=f;y=a;h=!0;try{a=!0;do a=c(!0,g);while(a&&!t);a||(f=null)}finally{y=-1,h=t=!1}}};b.unstable_flushUntilNextPaint=function(){if(h)throw Error("Already flushing work.");if(null!==f){var a=f;D=!0;E=!1;h=!0;try{var c=!0;do c=a(!0,
g);while(c&&!t);c||(f=null)}finally{h=t=D=!1}}};b.unstable_forceFrameRate=function(){};b.unstable_getCurrentPriorityLevel=function(){return e};b.unstable_getFirstCallbackNode=function(){return l(m)};b.unstable_next=function(a){switch(e){case 1:case 2:case 3:var c=3;break;default:c=e}var b=e;e=c;try{return a()}finally{e=b}};b.unstable_now=function(){return g};b.unstable_pauseExecution=function(){};b.unstable_requestPaint=function(){E=!0};b.unstable_runWithPriority=function(a,c){switch(a){case 1:case 2:case 3:case 4:case 5:break;
default:a=3}var b=e;e=a;try{return c()}finally{e=b}};b.unstable_scheduleCallback=function(a,c,b){var e=g;if("object"===typeof b&&null!==b){var d=b.delay;d="number"===typeof d&&0<d?e+d:e;b="number"===typeof b.timeout?b.timeout:K(a)}else b=K(a),d=e;b=d+b;a={id:L++,callback:c,priorityLevel:a,startTime:d,expirationTime:b,sortIndex:-1};d>e?(a.sortIndex=d,F(q,a),null===l(m)&&a===l(q)&&(x?(r=null,v=-1):x=!0,r=H,v=g+(d-e))):(a.sortIndex=b,F(m,a),u||C||(u=!0,f=I));return a};b.unstable_shouldYield=function(){var a=
g;B(a);var b=l(m);return b!==d&&null!==d&&null!==b&&null!==b.callback&&b.startTime<=a&&b.expirationTime<d.expirationTime||w()};b.unstable_wrapCallback=function(a){var b=e;return function(){var c=e;e=b;try{return a.apply(this,arguments)}finally{e=c}}};b.unstable_yieldValue=function(a){null===k?k=[a]:k.push(a)}});
