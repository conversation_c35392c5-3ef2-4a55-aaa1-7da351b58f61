import supabase from './supabaseClient';
import authService from './authService';

/**
 * Wallet Service
 * Handles all wallet-related operations including balance, transactions, and insights
 */
class WalletService {
  constructor() {
    this.currentUser = null;
  }

  /**
   * Initialize wallet service
   */
  initialize() {
    this.currentUser = authService.getCurrentUser();
  }

  /**
   * Get current user's wallet balance and details
   */
  async getWalletBalance() {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { data, error } = await supabase
        .from('payment_accounts')
        .select('*')
        .eq('user_id', user.id)
        .eq('account_type', 'wallet')
        .eq('is_primary', true)
        .single();

      if (error && error.code !== 'PGRST116') {
        throw error;
      }

      // If no wallet exists, create one
      if (!data) {
        const newWallet = await this.createWallet(user.id);
        if (newWallet.success) {
          return { success: true, data: newWallet.data };
        }
        return newWallet;
      }

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error getting wallet balance:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a new wallet for user
   */
  async createWallet(userId) {
    try {
      const walletData = {
        user_id: userId,
        account_type: 'wallet',
        provider_name: 'JiraniPay',
        account_number: this.generateAccountNumber(),
        account_name: 'JiraniPay Wallet',
        currency: 'UGX',
        is_primary: true,
        is_active: true,
        balance: 0.00,
        metadata: {
          created_via: 'app',
          wallet_type: 'primary'
        }
      };

      const { data, error } = await supabase
        .from('payment_accounts')
        .insert([walletData])
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Wallet created successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error creating wallet:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate unique account number
   */
  generateAccountNumber() {
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `JP${timestamp}${random}`;
  }

  /**
   * Get recent transactions
   */
  async getRecentTransactions(limit = 10) {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const { data, error } = await supabase
        .from('transactions')
        .select(`
          *,
          payment_accounts!payment_method_id(provider_name, account_name)
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return { success: true, data: data || [] };
    } catch (error) {
      console.error('❌ Error getting recent transactions:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get financial insights and analytics
   */
  async getFinancialInsights() {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      // Get transactions from last 30 days
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { data: transactions, error } = await supabase
        .from('transactions')
        .select('*')
        .eq('user_id', user.id)
        .eq('status', 'completed')
        .gte('created_at', thirtyDaysAgo.toISOString());

      if (error) throw error;

      // Calculate insights
      const insights = this.calculateInsights(transactions || []);
      
      return { success: true, data: insights };
    } catch (error) {
      console.error('❌ Error getting financial insights:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Calculate financial insights from transactions
   */
  calculateInsights(transactions) {
    const totalSpent = transactions
      .filter(t => ['bill_payment', 'airtime', 'transfer'].includes(t.transaction_type))
      .reduce((sum, t) => sum + parseFloat(t.amount), 0);

    const totalReceived = transactions
      .filter(t => t.transaction_type === 'deposit')
      .reduce((sum, t) => sum + parseFloat(t.amount), 0);

    const categorySpending = transactions
      .filter(t => ['bill_payment', 'airtime'].includes(t.transaction_type))
      .reduce((acc, t) => {
        const category = t.category || 'other';
        acc[category] = (acc[category] || 0) + parseFloat(t.amount);
        return acc;
      }, {});

    const topCategory = Object.entries(categorySpending)
      .sort(([,a], [,b]) => b - a)[0];

    const averageTransaction = transactions.length > 0 
      ? totalSpent / transactions.length 
      : 0;

    return {
      totalSpent,
      totalReceived,
      netFlow: totalReceived - totalSpent,
      transactionCount: transactions.length,
      averageTransaction,
      topSpendingCategory: topCategory ? {
        name: topCategory[0],
        amount: topCategory[1]
      } : null,
      categoryBreakdown: categorySpending,
      monthlyTrend: this.calculateMonthlyTrend(transactions)
    };
  }

  /**
   * Calculate monthly spending trend
   */
  calculateMonthlyTrend(transactions) {
    const currentMonth = new Date().getMonth();
    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;

    const currentMonthSpending = transactions
      .filter(t => new Date(t.created_at).getMonth() === currentMonth)
      .reduce((sum, t) => sum + parseFloat(t.amount), 0);

    const lastMonthSpending = transactions
      .filter(t => new Date(t.created_at).getMonth() === lastMonth)
      .reduce((sum, t) => sum + parseFloat(t.amount), 0);

    const percentageChange = lastMonthSpending > 0 
      ? ((currentMonthSpending - lastMonthSpending) / lastMonthSpending) * 100
      : 0;

    return {
      currentMonth: currentMonthSpending,
      lastMonth: lastMonthSpending,
      percentageChange,
      trend: percentageChange > 0 ? 'up' : percentageChange < 0 ? 'down' : 'stable'
    };
  }

  /**
   * Update wallet balance
   */
  async updateWalletBalance(amount, operation = 'add') {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const wallet = await this.getWalletBalance();
      if (!wallet.success) {
        return wallet;
      }

      const currentBalance = parseFloat(wallet.data.balance);
      const newBalance = operation === 'add' 
        ? currentBalance + parseFloat(amount)
        : currentBalance - parseFloat(amount);

      if (newBalance < 0) {
        return { success: false, error: 'Insufficient balance' };
      }

      const { data, error } = await supabase
        .from('payment_accounts')
        .update({ 
          balance: newBalance,
          last_balance_update: new Date().toISOString()
        })
        .eq('id', wallet.data.id)
        .select()
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error updating wallet balance:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create a new transaction record
   */
  async createTransaction(transactionData) {
    try {
      const user = authService.getCurrentUser();
      if (!user) {
        return { success: false, error: 'User not authenticated' };
      }

      const transaction = {
        user_id: user.id,
        reference_number: this.generateTransactionReference(),
        ...transactionData,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('transactions')
        .insert([transaction])
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Transaction created successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error creating transaction:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate unique transaction reference
   */
  generateTransactionReference() {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `TXN${timestamp}${random}`;
  }
}

// Export singleton instance
const walletService = new WalletService();
export default walletService;
