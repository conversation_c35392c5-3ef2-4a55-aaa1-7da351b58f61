import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';

const AIAssistantScreen = ({ navigation }) => {
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hello! I'm your JiraniPay AI Assistant. I'm here to help you with app navigation, transactions, account questions, and financial advice. How can I assist you today?",
      isBot: true,
      timestamp: new Date(),
    }
  ]);
  const [inputText, setInputText] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const scrollViewRef = useRef();

  const quickActions = [
    { id: 1, text: 'How to send money?', icon: 'send-outline' },
    { id: 2, text: 'Pay bills guide', icon: 'receipt-outline' },
    { id: 3, text: 'Add money to wallet', icon: 'wallet-outline' },
    { id: 4, text: 'Account security tips', icon: 'shield-checkmark-outline' },
  ];

  const botResponses = {
    'send money': "To send money: 1) Go to 'Send Money' on the home screen, 2) Enter recipient's phone number or scan QR code, 3) Enter amount, 4) Add a note (optional), 5) Confirm with your PIN. The transfer is instant between JiraniPay users!",
    'pay bills': "For bill payments: 1) Tap 'Bills' on home screen, 2) Select bill type (electricity, water, etc.), 3) Enter your account/meter number, 4) Confirm amount, 5) Pay with wallet balance. You'll get instant confirmation!",
    'add money': "To top up your wallet: 1) Go to 'Wallet' → 'Add Money', 2) Choose method: Bank transfer, Mobile money, or Agent, 3) Enter amount, 4) Follow prompts. Bank transfers are instant and free!",
    'security': "Keep your account secure: 1) Enable biometric login, 2) Use strong PIN, 3) Never share your PIN, 4) Enable transaction alerts, 5) Log out on shared devices. Report suspicious activity immediately!",
    'verification': "To verify your account: 1) Go to Profile → Account Verification, 2) Upload valid ID, 3) Provide proof of address, 4) Take a selfie. Verification increases your transaction limits and unlocks premium features!",
    'limits': "Transaction limits by verification level: Basic (UGX 500K/day), Verified (UGX 2M/day), Premium (UGX 10M/day). Verify your account to increase limits!",
    'fees': "JiraniPay fees: JiraniPay to JiraniPay transfers (FREE), Bank transfers (FREE), Mobile money (1-2%), Bill payments (FREE), Cash withdrawal at agents (1%). We keep fees low to help you save!",
    'default': "I understand you're asking about JiraniPay. Could you be more specific? I can help with: sending money, paying bills, wallet management, account security, verification, transaction limits, or fees. What would you like to know?"
  };

  const generateBotResponse = (userMessage) => {
    const message = userMessage.toLowerCase();
    
    if (message.includes('send') || message.includes('transfer') || message.includes('money')) {
      return botResponses['send money'];
    } else if (message.includes('bill') || message.includes('pay') || message.includes('electricity') || message.includes('water')) {
      return botResponses['pay bills'];
    } else if (message.includes('wallet') || message.includes('add') || message.includes('top up') || message.includes('deposit')) {
      return botResponses['add money'];
    } else if (message.includes('security') || message.includes('safe') || message.includes('protect')) {
      return botResponses['security'];
    } else if (message.includes('verify') || message.includes('verification') || message.includes('document')) {
      return botResponses['verification'];
    } else if (message.includes('limit') || message.includes('maximum') || message.includes('daily')) {
      return botResponses['limits'];
    } else if (message.includes('fee') || message.includes('cost') || message.includes('charge')) {
      return botResponses['fees'];
    } else {
      return botResponses['default'];
    }
  };

  const sendMessage = async () => {
    if (!inputText.trim()) return;

    const userMessage = {
      id: messages.length + 1,
      text: inputText.trim(),
      isBot: false,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsTyping(true);

    // Simulate AI thinking time
    setTimeout(() => {
      const botResponse = {
        id: messages.length + 2,
        text: generateBotResponse(inputText),
        isBot: true,
        timestamp: new Date(),
      };

      setMessages(prev => [...prev, botResponse]);
      setIsTyping(false);
    }, 1500);
  };

  const handleQuickAction = (actionText) => {
    setInputText(actionText);
  };

  useEffect(() => {
    scrollViewRef.current?.scrollToEnd({ animated: true });
  }, [messages]);

  const renderMessage = (message) => (
    <View
      key={message.id}
      style={[
        styles.messageContainer,
        message.isBot ? styles.botMessage : styles.userMessage
      ]}
    >
      {message.isBot && (
        <View style={styles.botAvatar}>
          <Ionicons name="chatbubble-ellipses" size={16} color={Colors.neutral.white} />
        </View>
      )}
      <View
        style={[
          styles.messageBubble,
          message.isBot ? styles.botBubble : styles.userBubble
        ]}
      >
        <Text style={[
          styles.messageText,
          message.isBot ? styles.botText : styles.userText
        ]}>
          {message.text}
        </Text>
        <Text style={[
          styles.timestamp,
          message.isBot ? styles.botTimestamp : styles.userTimestamp
        ]}>
          {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
        </Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
        </TouchableOpacity>
        <View style={styles.headerInfo}>
          <Text style={styles.headerTitle}>AI Assistant</Text>
          <Text style={styles.headerSubtitle}>Online • Ready to help</Text>
        </View>
        <View style={styles.aiIndicator}>
          <Ionicons name="sparkles" size={20} color={Colors.accent.gold} />
        </View>
      </View>

      <KeyboardAvoidingView 
        style={styles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          ref={scrollViewRef}
          style={styles.messagesContainer}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.messagesContent}
        >
          {messages.map(renderMessage)}
          
          {isTyping && (
            <View style={[styles.messageContainer, styles.botMessage]}>
              <View style={styles.botAvatar}>
                <Ionicons name="chatbubble-ellipses" size={16} color={Colors.neutral.white} />
              </View>
              <View style={[styles.messageBubble, styles.botBubble, styles.typingBubble]}>
                <Text style={styles.typingText}>AI is thinking...</Text>
              </View>
            </View>
          )}
        </ScrollView>

        {messages.length === 1 && (
          <View style={styles.quickActionsContainer}>
            <Text style={styles.quickActionsTitle}>Quick Actions</Text>
            <View style={styles.quickActionsGrid}>
              {quickActions.map((action) => (
                <TouchableOpacity
                  key={action.id}
                  style={styles.quickActionButton}
                  onPress={() => handleQuickAction(action.text)}
                >
                  <Ionicons name={action.icon} size={20} color={Colors.primary.main} />
                  <Text style={styles.quickActionText}>{action.text}</Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>
        )}

        <View style={styles.inputContainer}>
          <TextInput
            style={styles.textInput}
            value={inputText}
            onChangeText={setInputText}
            placeholder="Ask me anything about JiraniPay..."
            placeholderTextColor={Colors.neutral.warmGray}
            multiline
            maxLength={500}
          />
          <TouchableOpacity
            style={[styles.sendButton, !inputText.trim() && styles.sendButtonDisabled]}
            onPress={sendMessage}
            disabled={!inputText.trim()}
          >
            <Ionicons 
              name="send" 
              size={20} 
              color={inputText.trim() ? Colors.neutral.white : Colors.neutral.warmGray} 
            />
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.neutral.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  backButton: {
    padding: 8,
    marginRight: 12,
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  headerSubtitle: {
    fontSize: 14,
    color: Colors.status.success,
    marginTop: 2,
  },
  aiIndicator: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.accent.gold + '20',
    justifyContent: 'center',
    alignItems: 'center',
  },
  chatContainer: {
    flex: 1,
  },
  messagesContainer: {
    flex: 1,
  },
  messagesContent: {
    padding: 16,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  botMessage: {
    justifyContent: 'flex-start',
  },
  userMessage: {
    justifyContent: 'flex-end',
  },
  botAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.primary.main,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  messageBubble: {
    maxWidth: '80%',
    padding: 12,
    borderRadius: 16,
  },
  botBubble: {
    backgroundColor: Colors.neutral.white,
    borderBottomLeftRadius: 4,
  },
  userBubble: {
    backgroundColor: Colors.primary.main,
    borderBottomRightRadius: 4,
    marginLeft: 40,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  botText: {
    color: Colors.neutral.charcoal,
  },
  userText: {
    color: Colors.neutral.white,
  },
  timestamp: {
    fontSize: 12,
    marginTop: 4,
  },
  botTimestamp: {
    color: Colors.neutral.warmGray,
  },
  userTimestamp: {
    color: Colors.neutral.white + 'CC',
  },
  typingBubble: {
    backgroundColor: Colors.neutral.creamLight,
  },
  typingText: {
    color: Colors.neutral.warmGray,
    fontStyle: 'italic',
  },
  quickActionsContainer: {
    padding: 16,
    backgroundColor: Colors.neutral.white,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.creamDark,
  },
  quickActionsTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 12,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionButton: {
    width: '48%',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    backgroundColor: Colors.primary.light + '20',
    borderRadius: 8,
    marginBottom: 8,
  },
  quickActionText: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '500',
    marginLeft: 8,
    flex: 1,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    padding: 16,
    backgroundColor: Colors.neutral.white,
    borderTopWidth: 1,
    borderTopColor: Colors.neutral.creamDark,
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: Colors.neutral.charcoal,
    maxHeight: 100,
    marginRight: 12,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary.main,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: Colors.neutral.creamDark,
  },
});

export default AIAssistantScreen;
