/**
 * Internationalization (i18n) configuration for JiraniPay
 * Supports multiple East African languages for enhanced user experience
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// Language configurations with native names and country associations
export const SUPPORTED_LANGUAGES = {
  en: {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇬🇧',
    countries: ['UG', 'KE', 'TZ', 'RW', 'BI', 'SS', 'ET', 'SO', 'DJ', 'ER'],
    rtl: false
  },
  sw: {
    code: 'sw',
    name: 'Swahili',
    nativeName: 'Kiswahili',
    flag: '🇹🇿',
    countries: ['KE', 'TZ', 'UG'],
    rtl: false
  },
  fr: {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    countries: ['RW', 'BI', 'DJ'],
    rtl: false
  },
  ar: {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    countries: ['SS', 'SO', 'DJ', 'ER'],
    rtl: true
  },
  am: {
    code: 'am',
    name: 'Amharic',
    nativeName: 'አማርኛ',
    flag: '🇪🇹',
    countries: ['ET'],
    rtl: false
  },
  rw: {
    code: 'rw',
    name: 'Kinyarwanda',
    nativeName: 'Ikinyarwanda',
    flag: '🇷🇼',
    countries: ['RW'],
    rtl: false
  }
};

// Translation strings organized by screen/component
export const TRANSLATIONS = {
  en: {
    // Common
    common: {
      continue: 'Continue',
      cancel: 'Cancel',
      back: 'Back',
      next: 'Next',
      done: 'Done',
      loading: 'Loading...',
      error: 'Error',
      success: 'Success',
      retry: 'Retry',
      close: 'Close'
    },
    
    // Authentication
    auth: {
      welcomeBack: 'Welcome Back',
      goodMorning: 'Good Morning',
      goodAfternoon: 'Good Afternoon',
      goodEvening: 'Good Evening',
      chooseLoginMethod: 'Choose your login method',
      otpLogin: 'OTP Login',
      passwordLogin: 'Password Login',
      enterPhoneNumber: 'Enter phone number',
      enterPassword: 'Enter your password',
      forgotPassword: 'Forgot Password?',
      sendOTP: 'Send OTP',
      login: 'Login',
      verifyOTP: 'Verify OTP',
      resendOTP: 'Resend OTP',
      resendOTPIn: 'Resend OTP in',
      dontHaveAccount: "Don't have an account?",
      signUp: 'Sign Up',
      useBiometric: 'Use Biometric Login',
      
      // Registration
      createAccount: 'Create Account',
      getStarted: 'Enter your details to get started',
      fullName: 'Full Name',
      createPassword: 'Create Password',
      confirmPassword: 'Confirm Password',
      passwordRequirements: 'Password Requirements:',
      atLeast8Chars: '• At least 8 characters',
      containsNumber: '• Contains at least one number',
      containsSpecialChar: '• Contains at least one special character',
      passwordsMatch: '• Passwords match',
      alreadyHaveAccount: 'Already have an account?',
      signIn: 'Sign In',
      
      // Forgot Password
      resetPassword: 'Reset Password',
      enterPhoneToReset: 'Enter your phone number to reset your password',
      createNewPassword: 'Create New Password',
      newPassword: 'New Password',
      confirmNewPassword: 'Confirm New Password',
      
      // Country & Network
      selectCountry: 'Select Country',
      network: 'Network',
      
      // Validation Messages
      invalidPhone: 'Please enter a valid phone number',
      invalidCredentials: 'Invalid credentials',
      passwordTooShort: 'Password must be at least 8 characters long',
      passwordMismatch: 'Passwords do not match',
      nameRequired: 'Please enter your full name',
      otpSent: 'OTP sent to your phone number',
      otpInvalid: 'Invalid OTP',
      loginSuccessful: 'Login successful',
      accountCreated: 'Your account has been created successfully',
      passwordReset: 'Your password has been reset successfully'
    },
    
    // Time-based greetings with names
    greetings: {
      goodMorningName: 'Good Morning, {name}',
      goodAfternoonName: 'Good Afternoon, {name}',
      goodEveningName: 'Good Evening, {name}',
      goodMorningWelcome: 'Good Morning, Welcome Back',
      goodAfternoonWelcome: 'Good Afternoon, Welcome Back',
      goodEveningWelcome: 'Good Evening, Welcome Back'
    }
  },
  
  sw: {
    // Common
    common: {
      continue: 'Endelea',
      cancel: 'Ghairi',
      back: 'Rudi',
      next: 'Ifuatayo',
      done: 'Imemaliza',
      loading: 'Inapakia...',
      error: 'Hitilafu',
      success: 'Imefanikiwa',
      retry: 'Jaribu Tena',
      close: 'Funga'
    },
    
    // Authentication
    auth: {
      welcomeBack: 'Karibu Tena',
      goodMorning: 'Habari za Asubuhi',
      goodAfternoon: 'Habari za Mchana',
      goodEvening: 'Habari za Jioni',
      chooseLoginMethod: 'Chagua njia ya kuingia',
      otpLogin: 'Kuingia kwa OTP',
      passwordLogin: 'Kuingia kwa Nenosiri',
      enterPhoneNumber: 'Weka nambari ya simu',
      enterPassword: 'Weka nenosiri lako',
      forgotPassword: 'Umesahau Nenosiri?',
      sendOTP: 'Tuma OTP',
      login: 'Ingia',
      verifyOTP: 'Thibitisha OTP',
      resendOTP: 'Tuma OTP Tena',
      resendOTPIn: 'Tuma OTP tena baada ya',
      dontHaveAccount: 'Huna akaunti?',
      signUp: 'Jisajili',
      useBiometric: 'Tumia Alama za Kibiolojia',
      
      // Registration
      createAccount: 'Tengeneza Akaunti',
      getStarted: 'Weka maelezo yako ili kuanza',
      fullName: 'Jina Kamili',
      createPassword: 'Tengeneza Nenosiri',
      confirmPassword: 'Thibitisha Nenosiri',
      passwordRequirements: 'Mahitaji ya Nenosiri:',
      atLeast8Chars: '• Angalau herufi 8',
      containsNumber: '• Ina angalau nambari moja',
      containsSpecialChar: '• Ina angalau alama maalum moja',
      passwordsMatch: '• Nenosiri zinalingana',
      alreadyHaveAccount: 'Tayari una akaunti?',
      signIn: 'Ingia',
      
      // Forgot Password
      resetPassword: 'Weka Nenosiri Jipya',
      enterPhoneToReset: 'Weka nambari ya simu ili kubadilisha nenosiri',
      createNewPassword: 'Tengeneza Nenosiri Jipya',
      newPassword: 'Nenosiri Jipya',
      confirmNewPassword: 'Thibitisha Nenosiri Jipya',
      
      // Country & Network
      selectCountry: 'Chagua Nchi',
      network: 'Mtandao',
      
      // Validation Messages
      invalidPhone: 'Tafadhali weka nambari sahihi ya simu',
      invalidCredentials: 'Maelezo si sahihi',
      passwordTooShort: 'Nenosiri lazima liwe na angalau herufi 8',
      passwordMismatch: 'Nenosiri hazilingani',
      nameRequired: 'Tafadhali weka jina lako kamili',
      otpSent: 'OTP imetumwa kwenye simu yako',
      otpInvalid: 'OTP si sahihi',
      loginSuccessful: 'Umeingia kikamilifu',
      accountCreated: 'Akaunti yako imetengenezwa kikamilifu',
      passwordReset: 'Nenosiri lako limebadilishwa kikamilifu'
    },
    
    // Time-based greetings with names
    greetings: {
      goodMorningName: 'Habari za Asubuhi, {name}',
      goodAfternoonName: 'Habari za Mchana, {name}',
      goodEveningName: 'Habari za Jioni, {name}',
      goodMorningWelcome: 'Habari za Asubuhi, Karibu Tena',
      goodAfternoonWelcome: 'Habari za Mchana, Karibu Tena',
      goodEveningWelcome: 'Habari za Jioni, Karibu Tena'
    }
  },
  
  fr: {
    // Common
    common: {
      continue: 'Continuer',
      cancel: 'Annuler',
      back: 'Retour',
      next: 'Suivant',
      done: 'Terminé',
      loading: 'Chargement...',
      error: 'Erreur',
      success: 'Succès',
      retry: 'Réessayer',
      close: 'Fermer'
    },
    
    // Authentication
    auth: {
      welcomeBack: 'Bon Retour',
      goodMorning: 'Bonjour',
      goodAfternoon: 'Bon Après-midi',
      goodEvening: 'Bonsoir',
      chooseLoginMethod: 'Choisissez votre méthode de connexion',
      otpLogin: 'Connexion OTP',
      passwordLogin: 'Connexion par Mot de Passe',
      enterPhoneNumber: 'Entrez le numéro de téléphone',
      enterPassword: 'Entrez votre mot de passe',
      forgotPassword: 'Mot de Passe Oublié?',
      sendOTP: 'Envoyer OTP',
      login: 'Se Connecter',
      verifyOTP: 'Vérifier OTP',
      resendOTP: 'Renvoyer OTP',
      resendOTPIn: 'Renvoyer OTP dans',
      dontHaveAccount: "Vous n'avez pas de compte?",
      signUp: "S'inscrire",
      useBiometric: 'Utiliser la Connexion Biométrique',
      
      // Registration
      createAccount: 'Créer un Compte',
      getStarted: 'Entrez vos détails pour commencer',
      fullName: 'Nom Complet',
      createPassword: 'Créer un Mot de Passe',
      confirmPassword: 'Confirmer le Mot de Passe',
      passwordRequirements: 'Exigences du Mot de Passe:',
      atLeast8Chars: '• Au moins 8 caractères',
      containsNumber: '• Contient au moins un chiffre',
      containsSpecialChar: '• Contient au moins un caractère spécial',
      passwordsMatch: '• Les mots de passe correspondent',
      alreadyHaveAccount: 'Vous avez déjà un compte?',
      signIn: 'Se Connecter',
      
      // Forgot Password
      resetPassword: 'Réinitialiser le Mot de Passe',
      enterPhoneToReset: 'Entrez votre numéro pour réinitialiser le mot de passe',
      createNewPassword: 'Créer un Nouveau Mot de Passe',
      newPassword: 'Nouveau Mot de Passe',
      confirmNewPassword: 'Confirmer le Nouveau Mot de Passe',
      
      // Country & Network
      selectCountry: 'Sélectionner le Pays',
      network: 'Réseau',
      
      // Validation Messages
      invalidPhone: 'Veuillez entrer un numéro de téléphone valide',
      invalidCredentials: 'Identifiants invalides',
      passwordTooShort: 'Le mot de passe doit contenir au moins 8 caractères',
      passwordMismatch: 'Les mots de passe ne correspondent pas',
      nameRequired: 'Veuillez entrer votre nom complet',
      otpSent: 'OTP envoyé à votre numéro de téléphone',
      otpInvalid: 'OTP invalide',
      loginSuccessful: 'Connexion réussie',
      accountCreated: 'Votre compte a été créé avec succès',
      passwordReset: 'Votre mot de passe a été réinitialisé avec succès'
    },
    
    // Time-based greetings with names
    greetings: {
      goodMorningName: 'Bonjour, {name}',
      goodAfternoonName: 'Bon Après-midi, {name}',
      goodEveningName: 'Bonsoir, {name}',
      goodMorningWelcome: 'Bonjour, Bon Retour',
      goodAfternoonWelcome: 'Bon Après-midi, Bon Retour',
      goodEveningWelcome: 'Bonsoir, Bon Retour'
    }
  }
};

// Current language state
let currentLanguage = 'en';
let currentTranslations = TRANSLATIONS.en;

/**
 * Initialize i18n system
 * Loads saved language preference or detects from device/country
 */
export const initializeI18n = async (selectedCountryCode = 'UG') => {
  try {
    // Try to load saved language preference
    const savedLanguage = await AsyncStorage.getItem('selectedLanguage');

    if (savedLanguage && SUPPORTED_LANGUAGES[savedLanguage]) {
      currentLanguage = savedLanguage;
    } else {
      // Auto-detect based on country selection
      currentLanguage = getDefaultLanguageForCountry(selectedCountryCode);
    }

    currentTranslations = TRANSLATIONS[currentLanguage] || TRANSLATIONS.en;
    return currentLanguage;
  } catch (error) {
    console.error('Error initializing i18n:', error);
    currentLanguage = 'en';
    currentTranslations = TRANSLATIONS.en;
    return 'en';
  }
};

/**
 * Get default language for a country
 * @param {string} countryCode - Two letter country code
 * @returns {string} - Language code
 */
export const getDefaultLanguageForCountry = (countryCode) => {
  const languageMap = {
    'UG': 'en', // Uganda - English primary
    'KE': 'sw', // Kenya - Swahili primary
    'TZ': 'sw', // Tanzania - Swahili primary
    'RW': 'fr', // Rwanda - French/English (using French)
    'BI': 'fr', // Burundi - French
    'SS': 'ar', // South Sudan - Arabic/English (using Arabic)
    'ET': 'am', // Ethiopia - Amharic
    'SO': 'ar', // Somalia - Arabic/Somali (using Arabic)
    'DJ': 'fr', // Djibouti - French/Arabic (using French)
    'ER': 'ar'  // Eritrea - Arabic/Tigrinya (using Arabic)
  };

  return languageMap[countryCode] || 'en';
};

/**
 * Change current language
 * @param {string} languageCode - Language code to switch to
 */
export const changeLanguage = async (languageCode) => {
  if (SUPPORTED_LANGUAGES[languageCode]) {
    currentLanguage = languageCode;
    currentTranslations = TRANSLATIONS[languageCode] || TRANSLATIONS.en;

    // Save preference
    try {
      await AsyncStorage.setItem('selectedLanguage', languageCode);
    } catch (error) {
      console.error('Error saving language preference:', error);
    }
  }
};

/**
 * Get current language
 * @returns {string} - Current language code
 */
export const getCurrentLanguage = () => currentLanguage;

/**
 * Get current language configuration
 * @returns {Object} - Current language configuration
 */
export const getCurrentLanguageConfig = () => SUPPORTED_LANGUAGES[currentLanguage];

/**
 * Get available languages for a specific country
 * @param {string} countryCode - Two letter country code
 * @returns {Array} - Array of supported languages for the country
 */
export const getLanguagesForCountry = (countryCode) => {
  return Object.values(SUPPORTED_LANGUAGES).filter(lang =>
    lang.countries.includes(countryCode)
  );
};

/**
 * Translate a key with optional parameters
 * @param {string} key - Translation key (e.g., 'auth.login', 'common.continue')
 * @param {Object} params - Parameters for string interpolation
 * @returns {string} - Translated string
 */
export const t = (key, params = {}) => {
  const keys = key.split('.');
  let translation = currentTranslations;

  // Navigate through nested keys
  for (const k of keys) {
    translation = translation?.[k];
    if (!translation) break;
  }

  // Fallback to English if translation not found
  if (!translation) {
    let fallback = TRANSLATIONS.en;
    for (const k of keys) {
      fallback = fallback?.[k];
      if (!fallback) break;
    }
    translation = fallback || key;
  }

  // Handle string interpolation
  if (typeof translation === 'string' && params) {
    return translation.replace(/\{(\w+)\}/g, (match, paramKey) => {
      return params[paramKey] || match;
    });
  }

  return translation || key;
};

/**
 * Get time-based greeting with optional name
 * @param {string} name - Optional user name
 * @returns {string} - Localized greeting
 */
export const getTimeBasedGreeting = (name = null) => {
  const now = new Date();
  const hour = now.getHours();

  let timeKey;
  if (hour >= 5 && hour < 12) {
    timeKey = name ? 'goodMorningName' : 'goodMorningWelcome';
  } else if (hour >= 12 && hour < 17) {
    timeKey = name ? 'goodAfternoonName' : 'goodAfternoonWelcome';
  } else {
    timeKey = name ? 'goodEveningName' : 'goodEveningWelcome';
  }

  return t(`greetings.${timeKey}`, { name });
};

/**
 * Check if current language is RTL (Right-to-Left)
 * @returns {boolean} - True if current language is RTL
 */
export const isRTL = () => {
  return SUPPORTED_LANGUAGES[currentLanguage]?.rtl || false;
};

export default {
  initializeI18n,
  changeLanguage,
  getCurrentLanguage,
  getCurrentLanguageConfig,
  getLanguagesForCountry,
  getDefaultLanguageForCountry,
  getTimeBasedGreeting,
  isRTL,
  t,
  SUPPORTED_LANGUAGES,
  TRANSLATIONS
};
