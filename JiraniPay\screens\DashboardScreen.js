import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  RefreshControl,
  Dimensions,
  Animated,
  Alert,
} from 'react-native';
import { Ionicons, MaterialIcons, FontAwesome5 } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import authService from '../services/authService';
import walletService from '../services/walletService';
import {
  WalletCardSkeleton,
  QuickActionsSkeleton,
  TransactionsSkeleton
} from '../components/SkeletonLoader';

const { width, height } = Dimensions.get('window');

const DashboardScreen = ({ navigation }) => {
  const [user, setUser] = useState(null);
  const [walletData, setWalletData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [balanceVisible, setBalanceVisible] = useState(true);
  const [greeting, setGreeting] = useState('Good Morning, Friend!');
  const [recentTransactions, setRecentTransactions] = useState([]);
  const [financialInsights, setFinancialInsights] = useState(null);
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  const [dataLoaded, setDataLoaded] = useState(false);

  // Auto-scroll promotional banners state
  const promotionScrollRef = useRef(null);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const autoScrollTimer = useRef(null);
  const scrollPosition = useRef(0);

  useEffect(() => {
    initializeDashboard();
    startAnimations();
  }, []);

  useEffect(() => {
    if (user) {
      updateGreeting();
    }
  }, [user]);

  const startAnimations = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const initializeDashboard = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadUserData(),
        loadWalletData(),
        loadRecentTransactions(),
        loadFinancialInsights(),
      ]);
      setDataLoaded(true);
    } catch (error) {
      console.error('❌ Error initializing dashboard:', error);
      Alert.alert('Error', 'Failed to load dashboard data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = useCallback(async () => {
    setRefreshing(true);
    await initializeDashboard();
    setRefreshing(false);
  }, []);

  const updateGreeting = () => {
    const hour = new Date().getHours();
    let timeGreeting = 'Good Evening';
    if (hour >= 5 && hour < 12) timeGreeting = 'Good Morning';
    else if (hour >= 12 && hour < 17) timeGreeting = 'Good Afternoon';

    const userName = user?.user_metadata?.full_name || user?.email?.split('@')[0] || 'Friend';
    setGreeting(`${timeGreeting}, ${userName}!`);
  };

  const loadUserData = async () => {
    try {
      const currentUser = authService.getCurrentUser();
      if (currentUser) {
        setUser(currentUser);
        console.log('✅ User loaded:', currentUser.id);
      }
    } catch (error) {
      console.error('❌ Error loading user data:', error);
    }
  };

  const loadWalletData = async () => {
    try {
      const wallet = await walletService.getWalletBalance();
      if (wallet.success) {
        setWalletData(wallet.data);
        console.log('✅ Wallet loaded:', wallet.data);
      } else {
        console.log('⚠️ Wallet not found, will create default');
        setWalletData({ balance: 0, currency: 'UGX' });
      }
    } catch (error) {
      console.error('❌ Error loading wallet data:', error);
      setWalletData({ balance: 0, currency: 'UGX' });
    }
  };

  const loadRecentTransactions = async () => {
    try {
      const transactions = await walletService.getRecentTransactions(5);
      if (transactions.success) {
        setRecentTransactions(transactions.data);
      }
    } catch (error) {
      console.error('❌ Error loading transactions:', error);
    }
  };

  const loadFinancialInsights = async () => {
    try {
      const insights = await walletService.getFinancialInsights();
      if (insights.success) {
        setFinancialInsights(insights.data);
      } else {
        // Set default insights for new users
        setFinancialInsights({
          totalSpent: 0,
          totalReceived: 0,
          transactionCount: 0,
          topSpendingCategory: null,
          monthlyTrend: { trend: 'stable', percentageChange: 0 }
        });
      }
    } catch (error) {
      console.error('❌ Error loading insights:', error);
      // Set default insights on error
      setFinancialInsights({
        totalSpent: 0,
        totalReceived: 0,
        transactionCount: 0,
        topSpendingCategory: null,
        monthlyTrend: { trend: 'stable', percentageChange: 0 }
      });
    }
  };

  const handleQuickAction = (action) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    console.log('Quick action pressed:', action);

    // Map action IDs to user-friendly names
    const actionNames = {
      'buy_airtime': 'Buy Airtime',
      'buy_data': 'Buy Data Bundles',
      'pay_bills': 'Pay Bills',
      'send_money': 'Send Money',
      'mobile_money': 'Mobile Money',
      'qr_pay': 'QR Pay',
      'savings': 'Savings & Investment',
      'loans': 'Loans & Credit'
    };

    const actionName = actionNames[action] || action;

    // For now, show coming soon alerts with specific feature descriptions
    const descriptions = {
      'buy_airtime': 'Purchase airtime for MTN, Airtel, and UTL networks',
      'buy_data': 'Buy internet data bundles for all major carriers',
      'pay_bills': 'Pay electricity, water, and TV subscription bills',
      'send_money': 'Transfer money to friends and family instantly',
      'mobile_money': 'Deposit and withdraw from mobile money accounts',
      'qr_pay': 'Scan QR codes to pay merchants and businesses',
      'savings': 'Start saving and invest in mutual funds',
      'loans': 'Apply for quick loans and credit facilities'
    };

    Alert.alert(
      actionName,
      descriptions[action] || 'This feature will be available soon!',
      [
        { text: 'Cancel', style: 'cancel' },
        { text: 'Notify Me', onPress: () => console.log(`User interested in ${actionName}`) }
      ]
    );
  };

  const toggleBalanceVisibility = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setBalanceVisible(!balanceVisible);
  };

  const refreshBalance = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    await loadWalletData();
  };

  // Quick Actions Configuration - Airtel Style (2x4 Grid - 8 Actions Only)
  const quickActions = [
    {
      id: 'buy_data',
      title: 'Buy Bundles',
      icon: 'layers-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'send_money',
      title: 'Send Money',
      icon: 'arrow-up-circle-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'pay_bills',
      title: 'Pay Bill',
      icon: 'receipt-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'mobile_money',
      title: 'Airtel Money Pay',
      icon: 'card-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'refer_earn',
      title: 'Refer & Earn',
      icon: 'person-add-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'withdraw_cash',
      title: 'Withdraw cash',
      icon: 'cash-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'qr_pay',
      title: 'Scan & Pay',
      icon: 'scan-outline',
      iconType: 'Ionicons',
    },
    {
      id: 'buy_airtime',
      title: 'Buy Airtime',
      icon: 'phone-portrait-outline',
      iconType: 'Ionicons',
    },
  ];

  // Promotional Banners Configuration
  const promotionalBanners = [
    {
      id: 'school_fees',
      title: 'PAY SCHOOL\nFEES AND WIN',
      subtitle: '',
      prize: { amount: 'UGX\n100k', winners: '100\nWINNERS\nEVERY WEEK' },
      colors: ['#E53E3E', '#C53030'],
      emoji: '👩‍🎓',
      type: 'school'
    },
    {
      id: 'double_data',
      title: 'DOUBLE DATA\nWEEKEND',
      subtitle: 'Get 2x data on all bundles',
      colors: [Colors.primary.main, Colors.primary.dark],
      emoji: '📱',
      type: 'data'
    },
    {
      id: 'cashback',
      title: '5% CASHBACK\nON BILLS',
      subtitle: 'Pay utilities & earn back',
      colors: [Colors.secondary.savanna, Colors.accent.gold],
      emoji: '💰',
      type: 'cashback'
    }
  ];

  // Auto-scroll functionality for promotional banners
  const startAutoScroll = useCallback(() => {
    if (autoScrollTimer.current) {
      clearInterval(autoScrollTimer.current);
    }

    autoScrollTimer.current = setInterval(() => {
      if (!isUserScrolling && promotionScrollRef.current) {
        const bannerWidth = 280 + 16; // banner width + margin
        const totalWidth = bannerWidth * promotionalBanners.length;

        scrollPosition.current += bannerWidth;

        // Reset to beginning when reaching the end
        if (scrollPosition.current >= totalWidth) {
          scrollPosition.current = 0;
        }

        promotionScrollRef.current.scrollTo({
          x: scrollPosition.current,
          animated: true
        });
      }
    }, 3000); // 3 seconds per banner
  }, [isUserScrolling, promotionalBanners.length]);

  const stopAutoScroll = useCallback(() => {
    if (autoScrollTimer.current) {
      clearInterval(autoScrollTimer.current);
      autoScrollTimer.current = null;
    }
  }, []);

  const handleScrollBegin = () => {
    setIsUserScrolling(true);
    stopAutoScroll();
  };

  const handleScrollEnd = (event) => {
    setIsUserScrolling(false);
    scrollPosition.current = event.nativeEvent.contentOffset.x;

    // Resume auto-scroll after 2 seconds of user inactivity
    setTimeout(() => {
      if (!isUserScrolling) {
        startAutoScroll();
      }
    }, 2000);
  };

  // Start auto-scroll when component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      startAutoScroll();
    }, 1000); // Start after 1 second

    return () => {
      clearTimeout(timer);
      stopAutoScroll();
    };
  }, [startAutoScroll, stopAutoScroll]);

  // Render Icon Component
  const renderIcon = (iconName, iconType, size = 24, color = Colors.primary.main) => {
    switch (iconType) {
      case 'MaterialIcons':
        return <MaterialIcons name={iconName} size={size} color={color} />;
      case 'FontAwesome5':
        return <FontAwesome5 name={iconName} size={size} color={color} />;
      default:
        return <Ionicons name={iconName} size={size} color={color} />;
    }
  };

  if (loading) {
    return (
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header Skeleton */}
        <View style={styles.header}>
          <View style={styles.headerLeft}>
            <View style={[styles.skeletonBox, { width: 200, height: 28, marginBottom: 8 }]} />
            <View style={[styles.skeletonBox, { width: 150, height: 16 }]} />
          </View>
          <View style={styles.headerRight}>
            <View style={[styles.skeletonBox, { width: 40, height: 40, borderRadius: 20 }]} />
          </View>
        </View>

        <View style={styles.dashboard}>
          <WalletCardSkeleton />
          <QuickActionsSkeleton />
          <TransactionsSkeleton />
        </View>
      </ScrollView>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <Animated.View
        style={[
          styles.header,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        <View style={styles.headerLeft}>
          <Text style={styles.greeting}>{greeting}</Text>
          <Text style={styles.subtitle}>Welcome to JiraniPay</Text>
        </View>
        <View style={styles.headerRight}>
          <TouchableOpacity style={styles.iconButton}>
            <Ionicons name="notifications-outline" size={24} color={Colors.neutral.charcoal} />
            <View style={styles.notificationBadge} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.iconButton}>
            <Ionicons name="person-circle-outline" size={28} color={Colors.neutral.charcoal} />
          </TouchableOpacity>
        </View>
      </Animated.View>

      {/* Dashboard Content */}
      <Animated.View
        style={[
          styles.dashboard,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }],
          },
        ]}
      >
        {/* Modern Wallet Card */}
        <LinearGradient
          colors={Colors.gradients.sunset}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={styles.walletCard}
        >
          <View style={styles.walletHeader}>
            <View>
              <Text style={styles.walletTitle}>JiraniPay Wallet</Text>
              <Text style={styles.walletSubtitle}>Main Account</Text>
            </View>
            <View style={styles.walletActions}>
              <TouchableOpacity onPress={refreshBalance} style={styles.refreshButton}>
                <Ionicons name="refresh" size={20} color={Colors.neutral.white} />
              </TouchableOpacity>
              <TouchableOpacity onPress={toggleBalanceVisibility} style={styles.eyeButton}>
                <Ionicons
                  name={balanceVisible ? "eye-off" : "eye"}
                  size={20}
                  color={Colors.neutral.white}
                />
              </TouchableOpacity>
            </View>
          </View>

          <View style={styles.balanceContainer}>
            <Text style={styles.balance}>
              {balanceVisible
                ? `UGX ${(walletData?.balance || 0).toLocaleString('en-UG')}`
                : '••••••••'
              }
            </Text>
            <Text style={styles.balanceLabel}>Available Balance</Text>
          </View>

          <View style={styles.walletQuickActions}>
            <TouchableOpacity
              style={styles.walletButton}
              onPress={() => handleQuickAction('send_money')}
            >
              <Ionicons name="send" size={18} color={Colors.primary.main} />
              <Text style={styles.walletButtonText}>Send</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.walletButton}
              onPress={() => handleQuickAction('mobile_money')}
            >
              <Ionicons name="add" size={18} color={Colors.primary.main} />
              <Text style={styles.walletButtonText}>Top Up</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.walletButton}
              onPress={() => handleQuickAction('qr_pay')}
            >
              <Ionicons name="qr-code" size={18} color={Colors.primary.main} />
              <Text style={styles.walletButtonText}>QR Pay</Text>
            </TouchableOpacity>
          </View>
        </LinearGradient>

        {/* Airtel-Style Quick Actions Grid */}
        <View style={styles.quickActions}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Quick Actions</Text>
            <TouchableOpacity>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>
          <View style={styles.actionsGrid}>
            {quickActions.slice(0, 8).map((action, index) => (
              <TouchableOpacity
                key={action.id}
                style={styles.airtelActionCard}
                onPress={() => handleQuickAction(action.id)}
                activeOpacity={0.7}
              >
                <View style={styles.airtelIconContainer}>
                  {renderIcon(action.icon, action.iconType, 28, Colors.neutral.charcoal)}
                </View>
                <Text style={styles.airtelActionText}>{action.title}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Auto-Scrolling Promotional Banners - Airtel Style */}
        <View style={styles.promotionsSection}>
          <ScrollView
            ref={promotionScrollRef}
            horizontal
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.promotionsContainer}
            onScrollBeginDrag={handleScrollBegin}
            onScrollEndDrag={handleScrollEnd}
            onMomentumScrollEnd={handleScrollEnd}
            decelerationRate="fast"
          >
            {promotionalBanners.map((banner, index) => (
              <View key={banner.id} style={styles.promotionBanner}>
                <LinearGradient
                  colors={banner.colors}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 1 }}
                  style={styles.promotionGradient}
                >
                  <View style={styles.promotionContent}>
                    <View style={styles.promotionLeft}>
                      <Text style={styles.promotionTitle}>{banner.title}</Text>
                      {banner.subtitle && (
                        <Text style={styles.promotionSubtitle}>{banner.subtitle}</Text>
                      )}
                      {banner.prize && (
                        <View style={styles.promotionPrize}>
                          <Text style={styles.prizeAmount}>{banner.prize.amount}</Text>
                          <Text style={styles.prizeWinners}>{banner.prize.winners}</Text>
                        </View>
                      )}
                    </View>
                    <View style={styles.promotionRight}>
                      {banner.type === 'school' ? (
                        <View style={styles.studentImageContainer}>
                          <Text style={styles.studentEmoji}>{banner.emoji}</Text>
                        </View>
                      ) : (
                        <Text style={banner.type === 'data' ? styles.dataEmoji : styles.cashbackEmoji}>
                          {banner.emoji}
                        </Text>
                      )}
                    </View>
                  </View>
                </LinearGradient>
              </View>
            ))}
          </ScrollView>
        </View>

        {/* AI Financial Insights */}
        <View style={styles.insightsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Financial Insights</Text>
            <TouchableOpacity>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          {financialInsights ? (
            <View style={styles.insightsGrid}>
              <View style={styles.insightCard}>
                <View style={styles.insightHeader}>
                  <Ionicons name="trending-up" size={20} color={Colors.secondary.savanna} />
                  <Text style={styles.insightTitle}>Monthly Spending</Text>
                </View>
                <Text style={styles.insightValue}>
                  UGX {(financialInsights.totalSpent || 0).toLocaleString('en-UG')}
                </Text>
                <Text style={styles.insightSubtext}>
                  {financialInsights.monthlyTrend?.trend === 'up' ? '↗️' : '↘️'}
                  {Math.abs(financialInsights.monthlyTrend?.percentageChange || 0).toFixed(1)}% vs last month
                </Text>
              </View>

              <View style={styles.insightCard}>
                <View style={styles.insightHeader}>
                  <Ionicons name="pie-chart" size={20} color={Colors.accent.coral} />
                  <Text style={styles.insightTitle}>Top Category</Text>
                </View>
                <Text style={styles.insightValue}>
                  {financialInsights.topSpendingCategory?.name || 'N/A'}
                </Text>
                <Text style={styles.insightSubtext}>
                  UGX {(financialInsights.topSpendingCategory?.amount || 0).toLocaleString('en-UG')}
                </Text>
              </View>
            </View>
          ) : (
            <View style={styles.insightsPlaceholder}>
              <Ionicons name="analytics-outline" size={48} color={Colors.neutral.warmGray} />
              <Text style={styles.placeholderText}>Start spending to see insights</Text>
            </View>
          )}
        </View>

        {/* Recent Transactions */}
        <View style={styles.transactionsSection}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Transactions</Text>
            <TouchableOpacity>
              <Text style={styles.viewAllText}>View All</Text>
            </TouchableOpacity>
          </View>

          {recentTransactions.length > 0 ? (
            <View style={styles.transactionsList}>
              {recentTransactions.slice(0, 5).map((transaction, index) => (
                <View key={index} style={styles.transactionItem}>
                  <View style={styles.transactionIcon}>
                    <Ionicons
                      name={getTransactionIcon(transaction.transaction_type)}
                      size={20}
                      color={Colors.primary.main}
                    />
                  </View>
                  <View style={styles.transactionDetails}>
                    <Text style={styles.transactionTitle}>
                      {getTransactionTitle(transaction.transaction_type)}
                    </Text>
                    <Text style={styles.transactionDate}>
                      {new Date(transaction.created_at).toLocaleDateString()}
                    </Text>
                  </View>
                  <Text style={[
                    styles.transactionAmount,
                    { color: transaction.transaction_type === 'deposit'
                      ? Colors.secondary.savanna
                      : Colors.secondary.heritage
                    }
                  ]}>
                    {transaction.transaction_type === 'deposit' ? '+' : '-'}
                    UGX {parseFloat(transaction.amount).toLocaleString('en-UG')}
                  </Text>
                </View>
              ))}
            </View>
          ) : (
            <View style={styles.transactionsPlaceholder}>
              <Ionicons name="receipt-outline" size={48} color={Colors.neutral.warmGray} />
              <Text style={styles.placeholderText}>No transactions yet</Text>
              <Text style={styles.placeholderSubtext}>Your recent transactions will appear here</Text>
            </View>
          )}
        </View>
      </Animated.View>
    </ScrollView>
  );
};

// Helper functions for transaction display
const getTransactionIcon = (type) => {
  switch (type) {
    case 'airtime': return 'phone-portrait';
    case 'bill_payment': return 'receipt';
    case 'transfer': return 'send';
    case 'deposit': return 'add-circle';
    default: return 'swap-horizontal';
  }
};

const getTransactionTitle = (type) => {
  switch (type) {
    case 'airtime': return 'Airtime Purchase';
    case 'bill_payment': return 'Bill Payment';
    case 'transfer': return 'Money Transfer';
    case 'deposit': return 'Wallet Top-up';
    default: return 'Transaction';
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.neutral.cream,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.neutral.warmGray,
    fontWeight: '500',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
    backgroundColor: Colors.neutral.cream,
  },
  headerLeft: {
    flex: 1,
  },
  greeting: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
    fontWeight: '500',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    marginLeft: 16,
    padding: 8,
    borderRadius: 20,
    backgroundColor: Colors.neutral.white,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    position: 'relative',
  },
  notificationBadge: {
    position: 'absolute',
    top: 6,
    right: 6,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.secondary.heritage,
  },
  dashboard: {
    padding: 20,
    paddingBottom: 120,
  },

  // Modern Wallet Card Styles
  walletCard: {
    borderRadius: 20,
    padding: 24,
    marginBottom: 32,
    shadowColor: Colors.shadow.dark,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  walletHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 20,
  },
  walletTitle: {
    color: Colors.neutral.white,
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  walletSubtitle: {
    color: Colors.neutral.white,
    fontSize: 14,
    opacity: 0.8,
  },
  walletActions: {
    flexDirection: 'row',
    gap: 12,
  },
  refreshButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  eyeButton: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
  balanceContainer: {
    marginBottom: 24,
  },
  balance: {
    color: Colors.neutral.white,
    fontSize: 36,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  balanceLabel: {
    color: Colors.neutral.white,
    fontSize: 14,
    opacity: 0.8,
  },
  walletQuickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  walletButton: {
    flex: 1,
    backgroundColor: Colors.neutral.white,
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    marginHorizontal: 4,
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 6,
  },
  walletButtonText: {
    color: Colors.primary.main,
    fontWeight: '600',
    fontSize: 14,
  },
  // Quick Actions Styles
  quickActions: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  viewAllText: {
    fontSize: 16,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionCard: {
    width: (width - 72) / 4, // 4 columns with proper spacing
    backgroundColor: Colors.neutral.white,
    padding: 16,
    borderRadius: 16,
    alignItems: 'center',
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    marginBottom: 16,
  },
  actionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    textAlign: 'center',
    lineHeight: 16,
  },

  // Airtel-Style Action Cards (2x4 Grid)
  airtelActionCard: {
    width: (width - 64) / 4, // 4 columns with proper spacing for 2x4 grid
    backgroundColor: Colors.neutral.white,
    padding: 12,
    borderRadius: 12,
    alignItems: 'center',
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 12,
    minHeight: 85,
    justifyContent: 'center',
  },
  airtelIconContainer: {
    marginBottom: 8,
  },
  airtelActionText: {
    fontSize: 11,
    fontWeight: '500',
    color: Colors.neutral.charcoal,
    textAlign: 'center',
    lineHeight: 14,
  },

  // Promotional Banners Styles
  promotionsSection: {
    marginBottom: 32,
  },
  promotionsContainer: {
    paddingRight: 20,
  },
  promotionBanner: {
    width: 280,
    height: 120,
    marginRight: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  promotionGradient: {
    flex: 1,
    padding: 16,
  },
  promotionContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  promotionLeft: {
    flex: 1,
  },
  promotionTitle: {
    color: Colors.neutral.white,
    fontSize: 16,
    fontWeight: 'bold',
    lineHeight: 20,
    marginBottom: 4,
  },
  promotionSubtitle: {
    color: Colors.neutral.white,
    fontSize: 12,
    opacity: 0.9,
  },
  promotionPrize: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  prizeAmount: {
    color: Colors.neutral.white,
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 12,
    lineHeight: 16,
  },
  prizeWinners: {
    color: Colors.neutral.white,
    fontSize: 10,
    fontWeight: '600',
    lineHeight: 12,
  },
  promotionRight: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  studentImageContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  studentEmoji: {
    fontSize: 32,
  },
  dataEmoji: {
    fontSize: 40,
  },
  cashbackEmoji: {
    fontSize: 40,
  },
  // Financial Insights Styles
  insightsSection: {
    marginBottom: 32,
  },
  insightsGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  insightCard: {
    flex: 1,
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    padding: 20,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
  },
  insightValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  insightSubtext: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
  },
  insightsPlaceholder: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    padding: 40,
    alignItems: 'center',
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },

  // Transactions Styles
  transactionsSection: {
    marginBottom: 32,
  },
  transactionsList: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary.light + '20',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  transactionsPlaceholder: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 16,
    padding: 40,
    alignItems: 'center',
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  placeholderText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.warmGray,
    marginTop: 12,
    marginBottom: 4,
  },
  placeholderSubtext: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
  },

  // Skeleton Loading Styles
  skeletonBox: {
    backgroundColor: Colors.neutral.creamDark,
    borderRadius: 8,
  },
});

export default DashboardScreen;
