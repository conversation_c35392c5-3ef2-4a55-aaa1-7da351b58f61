import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  TextInput,
} from 'react-native';
// Removed PhoneInput to avoid native module issues in Expo Go
import OTPInputView from 'react-native-otp-inputs';
import { Ionicons } from '@expo/vector-icons';
import authService from '../services/authService';

const LoginScreen = ({ navigation }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [formattedValue, setFormattedValue] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [step, setStep] = useState('phone'); // 'phone' or 'otp'
  const [loading, setLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [networkProvider, setNetworkProvider] = useState('');
  const [biometricAvailable, setBiometricAvailable] = useState(false);

  useEffect(() => {
    checkBiometricAvailability();
    
    // Timer for resend OTP
    let interval = null;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
    } else if (interval) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  const checkBiometricAvailability = async () => {
    const biometric = await authService.isBiometricAvailable();
    setBiometricAvailable(biometric.available);
  };

  const handlePhoneNumberChange = (text) => {
    // Remove any non-digit characters and limit to 10 digits
    const cleaned = text.replace(/\D/g, '').substring(0, 10);
    setPhoneNumber(cleaned);
    setFormattedValue('+256' + cleaned);

    if (cleaned.length >= 9) {
      const provider = authService.detectNetworkProvider(cleaned);
      setNetworkProvider(provider);
    } else {
      setNetworkProvider('');
    }
  };

  const sendOTP = async () => {
    if (!phoneNumber || phoneNumber.length < 9) {
      Alert.alert('Error', 'Please enter a valid phone number');
      return;
    }

    setLoading(true);
    try {
      const result = await authService.sendOTP(phoneNumber);
      
      if (result.success) {
        setStep('otp');
        setResendTimer(60); // 60 seconds countdown
        Alert.alert('Success', 'OTP sent to your phone number');
      } else {
        Alert.alert('Error', result.error || 'Failed to send OTP');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const verifyOTP = async () => {
    if (!otpCode || otpCode.length !== 6) {
      Alert.alert('Error', 'Please enter the complete OTP');
      return;
    }

    setLoading(true);
    try {
      const result = await authService.verifyOTP(phoneNumber, otpCode);
      
      if (result.success) {
        // Check if user has profile, if not redirect to complete profile
        const profile = await authService.getUserProfile(result.data.user.id);
        
        if (profile.success) {
          // User exists, navigate to main app
          navigation.replace('MainApp');
        } else {
          // New user, navigate to complete profile
          navigation.navigate('CompleteProfile', { 
            phoneNumber,
            userId: result.data.user.id 
          });
        }
      } else {
        Alert.alert('Error', result.error || 'Invalid OTP');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleBiometricLogin = async () => {
    setLoading(true);
    try {
      const result = await authService.authenticateWithBiometric();
      
      if (result.success) {
        navigation.replace('MainApp');
      } else {
        Alert.alert('Authentication Failed', 'Please try again or use phone number login');
      }
    } catch (error) {
      Alert.alert('Error', 'Biometric authentication failed');
    } finally {
      setLoading(false);
    }
  };

  const resendOTP = async () => {
    if (resendTimer > 0) return;
    
    setLoading(true);
    try {
      const result = await authService.sendOTP(phoneNumber);
      
      if (result.success) {
        setResendTimer(60);
        Alert.alert('Success', 'OTP resent to your phone number');
      } else {
        Alert.alert('Error', result.error || 'Failed to resend OTP');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderPhoneStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.title}>Welcome Back</Text>
      <Text style={styles.subtitle}>Enter your phone number to continue</Text>

      <View style={styles.phoneInputContainer}>
        <View style={styles.phoneContainer}>
          <Text style={styles.countryCode}>+256</Text>
          <TextInput
            style={styles.phoneTextInput}
            value={phoneNumber}
            onChangeText={handlePhoneNumberChange}
            placeholder="Enter phone number"
            keyboardType="phone-pad"
            maxLength={10}
          />
        </View>

        {networkProvider && (
          <View style={styles.providerContainer}>
            <Text style={styles.providerText}>
              Network: <Text style={styles.providerName}>{networkProvider}</Text>
            </Text>
          </View>
        )}
      </View>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={sendOTP}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Send OTP</Text>
        )}
      </TouchableOpacity>

      {biometricAvailable && (
        <TouchableOpacity
          style={styles.biometricButton}
          onPress={handleBiometricLogin}
        >
          <Ionicons name="finger-print" size={24} color="#5B37B7" />
          <Text style={styles.biometricText}>Use Biometric Login</Text>
        </TouchableOpacity>
      )}

      <TouchableOpacity
        style={styles.registerLink}
        onPress={() => navigation.navigate('Register')}
      >
        <Text style={styles.registerText}>
          Don't have an account? <Text style={styles.registerTextBold}>Sign Up</Text>
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderOTPStep = () => (
    <View style={styles.stepContainer}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setStep('phone')}
      >
        <Ionicons name="arrow-back" size={24} color="#333" />
      </TouchableOpacity>

      <Text style={styles.title}>Verify OTP</Text>
      <Text style={styles.subtitle}>
        Enter the 6-digit code sent to {formattedValue || phoneNumber}
      </Text>

      <OTPInputView
        style={styles.otpContainer}
        pinCount={6}
        code={otpCode}
        onCodeChanged={setOtpCode}
        autoFocusOnLoad={true}
        inputStyles={styles.otpInput}
        inputContainerStyles={styles.otpInputContainer}
      />

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={verifyOTP}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Verify & Login</Text>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.resendButton, resendTimer > 0 && styles.resendButtonDisabled]}
        onPress={resendOTP}
        disabled={resendTimer > 0}
      >
        <Text style={[styles.resendText, resendTimer > 0 && styles.resendTextDisabled]}>
          {resendTimer > 0 ? `Resend OTP in ${resendTimer}s` : 'Resend OTP'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {step === 'phone' ? renderPhoneStep() : renderOTPStep()}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  backButton: {
    position: 'absolute',
    top: -50,
    left: 0,
    padding: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 22,
  },
  phoneInputContainer: {
    marginBottom: 30,
  },
  phoneContainer: {
    width: '100%',
    height: 60,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    backgroundColor: '#F8F9FA',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
  countryCode: {
    fontSize: 16,
    color: '#333',
    fontWeight: '600',
    marginRight: 10,
  },
  phoneTextInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    height: '100%',
  },
  providerContainer: {
    marginTop: 10,
    alignItems: 'center',
  },
  providerText: {
    fontSize: 14,
    color: '#666',
  },
  providerName: {
    fontWeight: 'bold',
    color: '#5B37B7',
  },
  button: {
    backgroundColor: '#5B37B7',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#5B37B7',
    marginBottom: 20,
  },
  biometricText: {
    color: '#5B37B7',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  registerLink: {
    alignItems: 'center',
  },
  registerText: {
    fontSize: 16,
    color: '#666',
  },
  registerTextBold: {
    fontWeight: 'bold',
    color: '#5B37B7',
  },
  otpContainer: {
    width: '100%',
    height: 80,
    marginBottom: 30,
  },
  otpInputContainer: {
    width: 45,
    height: 55,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
    justifyContent: 'center',
    alignItems: 'center',
  },
  otpInput: {
    color: '#333',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  resendButton: {
    alignItems: 'center',
    padding: 10,
  },
  resendButtonDisabled: {
    opacity: 0.5,
  },
  resendText: {
    fontSize: 16,
    color: '#5B37B7',
    fontWeight: '600',
  },
  resendTextDisabled: {
    color: '#999',
  },
});

export default LoginScreen;
