import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
  TextInput,
} from 'react-native';
// Removed PhoneInput to avoid native module issues in Expo Go
// Removed OTP input package to avoid native module issues
import { Ionicons } from '@expo/vector-icons';
import authService from '../services/authService';
import CountrySelector from '../components/CountrySelector';
import DevelopmentBanner from '../components/DevelopmentBanner';
import { Colors } from '../constants/Colors';
// Temporarily commented out for debugging
// import LanguageSelector from '../components/LanguageSelector';
// import { useLanguage } from '../contexts/LanguageContext';

const LoginScreen = ({ navigation }) => {
  // Temporarily commented out for debugging
  // const { t, getTimeBasedGreeting, initializeLanguage } = useLanguage();

  // Temporary translation function for debugging
  const t = (key) => {
    const translations = {
      'auth.chooseLoginMethod': 'Choose your login method',
      'auth.otpLogin': 'OTP Login',
      'auth.passwordLogin': 'Password Login',
      'auth.enterPhoneNumber': 'Enter phone number',
      'auth.network': 'Network',
      'auth.enterPassword': 'Enter your password',
      'auth.forgotPassword': 'Forgot Password?',
      'auth.sendOTP': 'Send OTP',
      'auth.login': 'Login',
      'auth.useBiometric': 'Use Biometric Login',
      'auth.dontHaveAccount': "Don't have an account?",
      'auth.signUp': 'Sign Up',
      'auth.verifyOTP': 'Verify OTP',
      'auth.resendOTPIn': 'Resend OTP in',
      'auth.resendOTP': 'Resend OTP',
      'common.error': 'Error',
      'auth.otpInvalid': 'Invalid OTP'
    };
    return translations[key] || key;
  };
  const [phoneNumber, setPhoneNumber] = useState('');
  const [formattedValue, setFormattedValue] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [password, setPassword] = useState('');
  const [step, setStep] = useState('phone'); // 'phone' or 'otp'
  const [loading, setLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [networkProvider, setNetworkProvider] = useState('');
  const [biometricAvailable, setBiometricAvailable] = useState(false);
  const [loginMethod, setLoginMethod] = useState('otp'); // 'otp' or 'password'
  const [showPassword, setShowPassword] = useState(false);
  const [greeting, setGreeting] = useState('');
  const [userName, setUserName] = useState('');
  const [selectedCountryCode, setSelectedCountryCode] = useState('UG'); // Default to Uganda

  useEffect(() => {
    checkBiometricAvailability();
    updateGreeting();

    // Timer for resend OTP
    let interval = null;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
    } else if (interval) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  useEffect(() => {
    // Update greeting every minute
    const greetingInterval = setInterval(() => {
      updateGreeting();
    }, 60000); // Update every minute

    // Initial greeting update
    updateGreeting();

    return () => clearInterval(greetingInterval);
  }, []);

  /**
   * Updates the greeting message with user context using translations
   */
  const updateGreeting = async () => {
    try {
      const currentUser = authService.getCurrentUser();

      if (currentUser) {
        // Try to get user profile for full name
        const profile = await authService.getUserProfile(currentUser.id);
        if (profile.success && profile.data?.full_name) {
          setUserName(profile.data.full_name);
          setGreeting(`Good Morning, ${profile.data.full_name}`);
        } else {
          setGreeting('Good Morning, Welcome Back');
        }
      } else {
        setGreeting('Good Morning, Welcome Back');
      }
    } catch (error) {
      // Fallback to basic greeting
      setGreeting(getTimeBasedGreeting());
    }
  };

  const checkBiometricAvailability = async () => {
    const biometric = await authService.isBiometricAvailable();
    setBiometricAvailable(biometric.available);
  };

  /**
   * Handles phone number input changes with country-specific formatting and network detection
   * @param {string} text - The input text
   */
  const handlePhoneNumberChange = (text) => {
    const countryConfig = authService.getCountryConfig(selectedCountryCode);
    if (!countryConfig) return;

    // Remove any non-digit characters and limit to country-specific length
    const cleaned = text.replace(/\D/g, '').substring(0, countryConfig.phoneLength);
    setPhoneNumber(cleaned);

    // Format with selected country code
    const formatted = authService.formatPhoneNumber(cleaned, selectedCountryCode);
    setFormattedValue(formatted);

    // Detect network provider for the selected country
    if (cleaned.length >= countryConfig.phoneLength - 1) {
      const provider = authService.detectNetworkProvider(cleaned, selectedCountryCode);
      setNetworkProvider(provider);
    } else {
      setNetworkProvider('');
    }
  };

  /**
   * Handles country selection change
   * @param {string} countryCode - Selected country code
   * @param {Object} country - Country configuration object
   */
  const handleCountrySelect = (countryCode, country) => {
    setSelectedCountryCode(countryCode);
    // Clear phone number and provider when country changes
    setPhoneNumber('');
    setFormattedValue('');
    setNetworkProvider('');
    // Temporarily commented out for debugging
    // initializeLanguage(countryCode);
  };

  /**
   * Handles language selection change
   * @param {string} languageCode - Selected language code
   * @param {Object} language - Language configuration object
   */
  const handleLanguageChange = (languageCode, language) => {
    // Update greeting with new language
    updateGreeting();
  };

  const handleOtpChange = (text, index) => {
    const newOtp = otpCode.split('');
    newOtp[index] = text;
    setOtpCode(newOtp.join(''));
  };

  const sendOTP = async () => {
    // Validate phone number for selected country
    const validation = authService.validatePhoneNumber(phoneNumber, selectedCountryCode);
    if (!validation.isValid) {
      Alert.alert('Error', validation.error);
      return;
    }

    setLoading(true);
    try {
      const result = await authService.sendOTP(phoneNumber, selectedCountryCode);

      if (result.success) {
        setStep('otp');
        setResendTimer(60); // 60 seconds countdown
        Alert.alert('Success', 'OTP sent to your phone number');
      } else {
        Alert.alert('Error', result.error || 'Failed to send OTP');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const loginWithPassword = async () => {
    // Validate phone number for selected country
    const validation = authService.validatePhoneNumber(phoneNumber, selectedCountryCode);
    if (!validation.isValid) {
      Alert.alert('Error', validation.error);
      return;
    }

    if (!password || password.length < 6) {
      Alert.alert('Error', 'Please enter your password');
      return;
    }

    setLoading(true);
    try {
      // TODO: Implement password login with Supabase
      const result = await authService.loginWithPassword(phoneNumber, password, selectedCountryCode);

      if (result.success) {
        navigation.replace('MainApp');
      } else {
        Alert.alert(t('common.error'), result.error || t('auth.invalidCredentials'));
      }
    } catch (error) {
      Alert.alert(t('common.error'), 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const verifyOTP = async () => {
    if (!otpCode || otpCode.length !== 6) {
      Alert.alert(t('common.error'), 'Please enter the complete OTP');
      return;
    }

    setLoading(true);
    try {
      const result = await authService.verifyOTP(phoneNumber, otpCode, selectedCountryCode);

      if (result.success) {
        // Check if user has profile, if not redirect to complete profile
        const profile = await authService.getUserProfile(result.data.user.id);

        if (profile.success) {
          // User exists, navigate to main app
          navigation.replace('MainApp');
        } else {
          // New user, navigate to complete profile
          navigation.navigate('CompleteProfile', {
            phoneNumber,
            userId: result.data.user.id
          });
        }
      } else {
        Alert.alert(t('common.error'), result.error || t('auth.otpInvalid'));
      }
    } catch (error) {
      Alert.alert(t('common.error'), 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleBiometricLogin = async () => {
    setLoading(true);
    try {
      const result = await authService.authenticateWithBiometric();
      
      if (result.success) {
        navigation.replace('MainApp');
      } else {
        Alert.alert('Authentication Failed', 'Please try again or use phone number login');
      }
    } catch (error) {
      Alert.alert('Error', 'Biometric authentication failed');
    } finally {
      setLoading(false);
    }
  };

  const resendOTP = async () => {
    if (resendTimer > 0) return;
    
    setLoading(true);
    try {
      const result = await authService.sendOTP(phoneNumber, selectedCountryCode);
      
      if (result.success) {
        setResendTimer(60);
        Alert.alert('Success', 'OTP resent to your phone number');
      } else {
        Alert.alert('Error', result.error || 'Failed to resend OTP');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderPhoneStep = () => (
    <View style={styles.stepContainer}>
      {/* Language Selector - Temporarily commented out for debugging */}
      {/* <View style={styles.headerContainer}>
        <View style={styles.headerSpacer} />
        <LanguageSelector
          selectedCountryCode={selectedCountryCode}
          onLanguageChange={handleLanguageChange}
          compact={true}
        />
      </View> */}

      <Text style={styles.title}>{greeting}</Text>
      <Text style={styles.subtitle}>{t('auth.chooseLoginMethod')}</Text>

      {/* Login Method Toggle */}
      <View style={styles.toggleContainer}>
        <TouchableOpacity
          style={[styles.toggleButton, loginMethod === 'otp' && styles.toggleButtonActive]}
          onPress={() => setLoginMethod('otp')}
        >
          <Text style={[styles.toggleText, loginMethod === 'otp' && styles.toggleTextActive]}>
            {t('auth.otpLogin')}
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.toggleButton, loginMethod === 'password' && styles.toggleButtonActive]}
          onPress={() => setLoginMethod('password')}
        >
          <Text style={[styles.toggleText, loginMethod === 'password' && styles.toggleTextActive]}>
            {t('auth.passwordLogin')}
          </Text>
        </TouchableOpacity>
      </View>

      {/* Country Selector */}
      <CountrySelector
        selectedCountryCode={selectedCountryCode}
        onCountrySelect={handleCountrySelect}
      />

      <View style={styles.phoneInputContainer}>
        <View style={styles.phoneContainer}>
          <Text style={styles.countryCode}>
            {authService.getCountryConfig(selectedCountryCode)?.code || '+256'}
          </Text>
          <TextInput
            style={styles.phoneTextInput}
            value={phoneNumber}
            onChangeText={handlePhoneNumberChange}
            placeholder={`${t('auth.enterPhoneNumber')} (e.g., ${authService.getCountryConfig(selectedCountryCode)?.example || '777123456'})`}
            keyboardType="phone-pad"
            maxLength={authService.getCountryConfig(selectedCountryCode)?.phoneLength || 10}
          />
        </View>

        {networkProvider && (
          <View style={styles.providerContainer}>
            <Text style={styles.providerText}>
              {t('auth.network')}: <Text style={styles.providerName}>{networkProvider}</Text>
            </Text>
          </View>
        )}
      </View>

      {/* Password Input for Password Login */}
      {loginMethod === 'password' && (
        <View style={styles.passwordContainer}>
          <View style={styles.passwordInputContainer}>
            <TextInput
              style={styles.passwordInput}
              value={password}
              onChangeText={setPassword}
              placeholder={t('auth.enterPassword')}
              secureTextEntry={!showPassword}
              autoCapitalize="none"
            />
            <TouchableOpacity
              style={styles.eyeButton}
              onPress={() => setShowPassword(!showPassword)}
            >
              <Ionicons
                name={showPassword ? "eye-off-outline" : "eye-outline"}
                size={20}
                color="#666"
              />
            </TouchableOpacity>
          </View>

          {/* Forgot Password Link */}
          <TouchableOpacity
            style={styles.forgotPasswordContainer}
            onPress={() => navigation.navigate('ForgotPassword')}
          >
            <Text style={styles.forgotPasswordText}>{t('auth.forgotPassword')}</Text>
          </TouchableOpacity>
        </View>
      )}

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={loginMethod === 'otp' ? sendOTP : loginWithPassword}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>
            {loginMethod === 'otp' ? t('auth.sendOTP') : t('auth.login')}
          </Text>
        )}
      </TouchableOpacity>

      {biometricAvailable && (
        <TouchableOpacity
          style={styles.biometricButton}
          onPress={handleBiometricLogin}
        >
          <Ionicons name="finger-print" size={24} color={Colors.primary.main} />
          <Text style={styles.biometricText}>{t('auth.useBiometric')}</Text>
        </TouchableOpacity>
      )}

      <TouchableOpacity
        style={styles.registerLink}
        onPress={() => navigation.navigate('Register')}
      >
        <Text style={styles.registerText}>
          {t('auth.dontHaveAccount')} <Text style={styles.registerTextBold}>{t('auth.signUp')}</Text>
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderOTPStep = () => (
    <View style={styles.stepContainer}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setStep('phone')}
      >
        <Ionicons name="arrow-back" size={24} color="#333" />
      </TouchableOpacity>

      <Text style={styles.title}>{t('auth.verifyOTP')}</Text>
      <Text style={styles.subtitle}>
        Enter the 6-digit code sent to {formattedValue || phoneNumber}
      </Text>

      <View style={styles.otpContainer}>
        {[0, 1, 2, 3, 4, 5].map((index) => (
          <TextInput
            key={index}
            style={styles.otpInput}
            value={otpCode[index] || ''}
            onChangeText={(text) => handleOtpChange(text, index)}
            keyboardType="numeric"
            maxLength={1}
            textAlign="center"
          />
        ))}
      </View>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={verifyOTP}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>{t('auth.verifyOTP')} & {t('auth.login')}</Text>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.resendButton, resendTimer > 0 && styles.resendButtonDisabled]}
        onPress={resendOTP}
        disabled={resendTimer > 0}
      >
        <Text style={[styles.resendText, resendTimer > 0 && styles.resendTextDisabled]}>
          {resendTimer > 0 ? `${t('auth.resendOTPIn')} ${resendTimer}s` : t('auth.resendOTP')}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <DevelopmentBanner visible={true} />
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {step === 'phone' ? renderPhoneStep() : renderOTPStep()}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    position: 'absolute',
    top: -80,
    left: 0,
    right: 0,
    paddingHorizontal: 20,
  },
  headerSpacer: {
    flex: 1,
  },
  backButton: {
    position: 'absolute',
    top: -50,
    left: 0,
    padding: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 22,
  },
  toggleContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    padding: 4,
    marginBottom: 30,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  toggleButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
  },
  toggleButtonActive: {
    backgroundColor: Colors.primary.main,
    shadowColor: Colors.primary.dark,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  toggleText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.neutral.warmGray,
  },
  toggleTextActive: {
    color: Colors.neutral.white,
  },
  phoneInputContainer: {
    marginBottom: 20,
  },
  phoneContainer: {
    width: '100%',
    height: 60,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.neutral.warmGrayLight,
    backgroundColor: Colors.neutral.white,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  countryCode: {
    fontSize: 16,
    color: Colors.neutral.charcoal,
    fontWeight: '600',
    marginRight: 10,
  },
  phoneTextInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.neutral.charcoal,
    height: '100%',
  },
  passwordContainer: {
    marginBottom: 20,
  },
  passwordInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.neutral.warmGrayLight,
    borderRadius: 12,
    backgroundColor: Colors.neutral.white,
    paddingHorizontal: 16,
    height: 60,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  passwordInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.neutral.charcoal,
    height: '100%',
  },
  eyeButton: {
    padding: 4,
  },
  forgotPasswordContainer: {
    alignItems: 'flex-end',
    marginTop: 10,
  },
  forgotPasswordText: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  providerContainer: {
    marginTop: 10,
    alignItems: 'center',
  },
  providerText: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  providerName: {
    fontWeight: 'bold',
    color: Colors.primary.main,
  },
  button: {
    backgroundColor: Colors.primary.main,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: Colors.primary.dark,
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 4,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: Colors.neutral.white,
    fontSize: 16,
    fontWeight: 'bold',
  },
  biometricButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.primary.main,
    backgroundColor: Colors.neutral.white,
    marginBottom: 20,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  biometricText: {
    color: Colors.primary.main,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  registerLink: {
    alignItems: 'center',
  },
  registerText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
  },
  registerTextBold: {
    fontWeight: 'bold',
    color: Colors.primary.main,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: 30,
  },
  otpInput: {
    width: 45,
    height: 55,
    borderWidth: 1,
    borderColor: Colors.neutral.warmGrayLight,
    borderRadius: 12,
    backgroundColor: Colors.neutral.white,
    color: Colors.neutral.charcoal,
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  resendButton: {
    alignItems: 'center',
    padding: 10,
  },
  resendButtonDisabled: {
    opacity: 0.5,
  },
  resendText: {
    fontSize: 16,
    color: Colors.primary.main,
    fontWeight: '600',
  },
  resendTextDisabled: {
    color: Colors.neutral.warmGray,
  },
});

export default LoginScreen;
