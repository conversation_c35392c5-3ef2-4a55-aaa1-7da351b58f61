import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import PhoneInput from 'react-native-phone-number-input';
import OTPInputView from '@twotalltotems/react-native-otp-input';
import { Ionicons } from '@expo/vector-icons';
import authService from '../services/authService';

const RegisterScreen = ({ navigation }) => {
  const [phoneNumber, setPhoneNumber] = useState('');
  const [formattedValue, setFormattedValue] = useState('');
  const [otpCode, setOtpCode] = useState('');
  const [step, setStep] = useState('phone'); // 'phone' or 'otp'
  const [loading, setLoading] = useState(false);
  const [resendTimer, setResendTimer] = useState(0);
  const [networkProvider, setNetworkProvider] = useState('');

  useEffect(() => {
    // Timer for resend OTP
    let interval = null;
    if (resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer(resendTimer - 1);
      }, 1000);
    } else if (interval) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [resendTimer]);

  const handlePhoneNumberChange = (text) => {
    setPhoneNumber(text);
    if (text.length >= 9) {
      const provider = authService.detectNetworkProvider(text);
      setNetworkProvider(provider);
    } else {
      setNetworkProvider('');
    }
  };

  const sendOTP = async () => {
    if (!phoneNumber || phoneNumber.length < 9) {
      Alert.alert('Error', 'Please enter a valid phone number');
      return;
    }

    setLoading(true);
    try {
      const result = await authService.sendOTP(phoneNumber);
      
      if (result.success) {
        setStep('otp');
        setResendTimer(60); // 60 seconds countdown
        Alert.alert('Success', 'OTP sent to your phone number');
      } else {
        Alert.alert('Error', result.error || 'Failed to send OTP');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const verifyOTPAndProceed = async () => {
    if (!otpCode || otpCode.length !== 6) {
      Alert.alert('Error', 'Please enter the complete OTP');
      return;
    }

    setLoading(true);
    try {
      const result = await authService.verifyOTP(phoneNumber, otpCode);
      
      if (result.success) {
        // Navigate to complete profile screen for new users
        navigation.navigate('CompleteProfile', { 
          phoneNumber,
          userId: result.data.user.id,
          isNewUser: true
        });
      } else {
        Alert.alert('Error', result.error || 'Invalid OTP');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const resendOTP = async () => {
    if (resendTimer > 0) return;
    
    setLoading(true);
    try {
      const result = await authService.sendOTP(phoneNumber);
      
      if (result.success) {
        setResendTimer(60);
        Alert.alert('Success', 'OTP resent to your phone number');
      } else {
        Alert.alert('Error', result.error || 'Failed to resend OTP');
      }
    } catch (error) {
      Alert.alert('Error', 'Something went wrong. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderPhoneStep = () => (
    <View style={styles.stepContainer}>
      <Text style={styles.title}>Create Account</Text>
      <Text style={styles.subtitle}>Enter your phone number to get started</Text>

      <View style={styles.phoneInputContainer}>
        <PhoneInput
          ref={null}
          defaultValue={phoneNumber}
          defaultCode="UG"
          layout="first"
          onChangeText={handlePhoneNumberChange}
          onChangeFormattedText={setFormattedValue}
          placeholder="Enter phone number"
          containerStyle={styles.phoneContainer}
          textContainerStyle={styles.phoneTextContainer}
          textInputStyle={styles.phoneTextInput}
          codeTextStyle={styles.phoneCodeText}
        />
        
        {networkProvider && (
          <View style={styles.providerContainer}>
            <Text style={styles.providerText}>
              Network: <Text style={styles.providerName}>{networkProvider}</Text>
            </Text>
          </View>
        )}
      </View>

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={sendOTP}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Send OTP</Text>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.loginLink}
        onPress={() => navigation.navigate('Login')}
      >
        <Text style={styles.loginText}>
          Already have an account? <Text style={styles.loginTextBold}>Sign In</Text>
        </Text>
      </TouchableOpacity>
    </View>
  );

  const renderOTPStep = () => (
    <View style={styles.stepContainer}>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setStep('phone')}
      >
        <Ionicons name="arrow-back" size={24} color="#333" />
      </TouchableOpacity>

      <Text style={styles.title}>Verify Phone Number</Text>
      <Text style={styles.subtitle}>
        Enter the 6-digit code sent to {formattedValue || phoneNumber}
      </Text>

      <OTPInputView
        style={styles.otpContainer}
        pinCount={6}
        code={otpCode}
        onCodeChanged={setOtpCode}
        autoFocusOnLoad
        codeInputFieldStyle={styles.otpInput}
        codeInputHighlightStyle={styles.otpInputHighlight}
      />

      <TouchableOpacity
        style={[styles.button, loading && styles.buttonDisabled]}
        onPress={verifyOTPAndProceed}
        disabled={loading}
      >
        {loading ? (
          <ActivityIndicator color="#fff" />
        ) : (
          <Text style={styles.buttonText}>Verify & Continue</Text>
        )}
      </TouchableOpacity>

      <TouchableOpacity
        style={[styles.resendButton, resendTimer > 0 && styles.resendButtonDisabled]}
        onPress={resendOTP}
        disabled={resendTimer > 0}
      >
        <Text style={[styles.resendText, resendTimer > 0 && styles.resendTextDisabled]}>
          {resendTimer > 0 ? `Resend OTP in ${resendTimer}s` : 'Resend OTP'}
        </Text>
      </TouchableOpacity>
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer}>
        {step === 'phone' ? renderPhoneStep() : renderOTPStep()}
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContainer: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: 20,
  },
  stepContainer: {
    flex: 1,
    justifyContent: 'center',
  },
  backButton: {
    position: 'absolute',
    top: -50,
    left: 0,
    padding: 10,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginBottom: 10,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 22,
  },
  phoneInputContainer: {
    marginBottom: 30,
  },
  phoneContainer: {
    width: '100%',
    height: 60,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    backgroundColor: '#F8F9FA',
  },
  phoneTextContainer: {
    backgroundColor: 'transparent',
    borderRadius: 12,
  },
  phoneTextInput: {
    fontSize: 16,
    color: '#333',
  },
  phoneCodeText: {
    fontSize: 16,
    color: '#333',
  },
  providerContainer: {
    marginTop: 10,
    alignItems: 'center',
  },
  providerText: {
    fontSize: 14,
    color: '#666',
  },
  providerName: {
    fontWeight: 'bold',
    color: '#5B37B7',
  },
  button: {
    backgroundColor: '#5B37B7',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 20,
  },
  buttonDisabled: {
    opacity: 0.6,
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  loginLink: {
    alignItems: 'center',
  },
  loginText: {
    fontSize: 16,
    color: '#666',
  },
  loginTextBold: {
    fontWeight: 'bold',
    color: '#5B37B7',
  },
  otpContainer: {
    width: '100%',
    height: 80,
    marginBottom: 30,
  },
  otpInput: {
    width: 45,
    height: 55,
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    backgroundColor: '#F8F9FA',
    color: '#333',
    fontSize: 18,
    fontWeight: 'bold',
  },
  otpInputHighlight: {
    borderColor: '#5B37B7',
    backgroundColor: '#fff',
  },
  resendButton: {
    alignItems: 'center',
    padding: 10,
  },
  resendButtonDisabled: {
    opacity: 0.5,
  },
  resendText: {
    fontSize: 16,
    color: '#5B37B7',
    fontWeight: '600',
  },
  resendTextDisabled: {
    color: '#999',
  },
});

export default RegisterScreen;
