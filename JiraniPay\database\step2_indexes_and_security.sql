-- =====================================================
-- JiraniPay Database Setup - Step 2: Indexes and Security
-- =====================================================
-- Run this script AFTER step1_extensions_and_tables.sql
-- =====================================================

-- =====================================================
-- INDEXES FOR PERFORMANCE
-- =====================================================

-- User Profiles indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_phone ON public.user_profiles(phone_number);
CREATE INDEX IF NOT EXISTS idx_user_profiles_country ON public.user_profiles(country_code);
CREATE INDEX IF NOT EXISTS idx_user_profiles_verification ON public.user_profiles(is_verified, verification_level);

-- Payment Accounts indexes
CREATE INDEX IF NOT EXISTS idx_payment_accounts_user ON public.payment_accounts(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_accounts_type ON public.payment_accounts(account_type);
CREATE INDEX IF NOT EXISTS idx_payment_accounts_provider ON public.payment_accounts(provider_name);
CREATE INDEX IF NOT EXISTS idx_payment_accounts_active ON public.payment_accounts(is_active);

-- Transactions indexes
CREATE INDEX IF NOT EXISTS idx_transactions_user ON public.transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_transactions_status ON public.transactions(status);
CREATE INDEX IF NOT EXISTS idx_transactions_type ON public.transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_transactions_created ON public.transactions(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_transactions_reference ON public.transactions(reference_number);

-- Bill Providers indexes
CREATE INDEX IF NOT EXISTS idx_bill_providers_country ON public.bill_providers(country_code);
CREATE INDEX IF NOT EXISTS idx_bill_providers_category ON public.bill_providers(category);
CREATE INDEX IF NOT EXISTS idx_bill_providers_active ON public.bill_providers(is_active);

-- =====================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_preferences ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.payment_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.bill_providers ENABLE ROW LEVEL SECURITY;

-- User Profiles policies
CREATE POLICY "Users can view own profile" ON public.user_profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.user_profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.user_profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- User Preferences policies
CREATE POLICY "Users can manage own preferences" ON public.user_preferences
    FOR ALL USING (auth.uid() = id);

-- Payment Accounts policies
CREATE POLICY "Users can view own payment accounts" ON public.payment_accounts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own payment accounts" ON public.payment_accounts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own payment accounts" ON public.payment_accounts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own payment accounts" ON public.payment_accounts
    FOR DELETE USING (auth.uid() = user_id);

-- Transactions policies
CREATE POLICY "Users can view own transactions" ON public.transactions
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own transactions" ON public.transactions
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Bill Providers policies (read-only for all authenticated users)
CREATE POLICY "Authenticated users can view bill providers" ON public.bill_providers
    FOR SELECT USING (auth.role() = 'authenticated');

-- Success message
SELECT 'Step 2 Complete: Indexes and security policies created successfully!' as status;
