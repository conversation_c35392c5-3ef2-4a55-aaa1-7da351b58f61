import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { getCountriesArray, getCountryByCode } from '../utils/countriesConfig';

const { height: screenHeight } = Dimensions.get('window');

/**
 * Reusable Country Selector Component
 * Provides dropdown/picker functionality for East African countries
 * 
 * @param {Object} props - Component props
 * @param {string} props.selectedCountryCode - Currently selected country code (e.g., 'UG')
 * @param {Function} props.onCountrySelect - Callback when country is selected
 * @param {Object} props.style - Additional styles for the container
 * @param {boolean} props.disabled - Whether the selector is disabled
 */
const CountrySelector = ({
  selectedCountryCode = 'UG',
  onCountrySelect,
  style = {},
  disabled = false
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [countries] = useState(getCountriesArray());
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;

  const selectedCountry = getCountryByCode(selectedCountryCode);

  /**
   * Opens the country selector modal with animation
   */
  const openSelector = () => {
    if (disabled) return;

    setIsVisible(true);

    // Animate modal slide up
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  /**
   * Closes the country selector modal with animation
   */
  const closeSelector = () => {
    Animated.timing(slideAnim, {
      toValue: screenHeight,
      duration: 250,
      useNativeDriver: true,
    }).start(() => {
      setIsVisible(false);
    });
  };

  /**
   * Handles country selection
   * @param {Object} country - Selected country object
   */
  const handleCountrySelect = (country) => {
    onCountrySelect && onCountrySelect(country.countryCode, country);
    closeSelector();
  };

  /**
   * Renders individual country item in the list
   * @param {Object} item - Country object
   * @returns {JSX.Element} - Country item component
   */
  const renderCountryItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.countryItem,
        selectedCountryCode === item.countryCode && styles.selectedCountryItem
      ]}
      onPress={() => handleCountrySelect(item)}
    >
      <View style={styles.countryItemContent}>
        <Text style={styles.countryFlag}>{item.flag}</Text>
        <View style={styles.countryInfo}>
          <Text style={styles.countryName}>{item.name}</Text>
          <Text style={styles.countryCodeText}>{item.code}</Text>
        </View>
        {selectedCountryCode === item.countryCode && (
          <Ionicons name="checkmark" size={20} color={Colors.primary.main} />
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, style]}>
      {/* Country Selector Button */}
      <TouchableOpacity
        style={[styles.selectorButton, disabled && styles.disabledButton]}
        onPress={openSelector}
        disabled={disabled}
      >
        <View style={styles.selectedCountryContent}>
          <Text style={styles.selectedFlag}>{selectedCountry?.flag}</Text>
          <Text style={styles.selectedCountryName}>{selectedCountry?.name}</Text>
          <Text style={styles.selectedCountryCode}>{selectedCountry?.code}</Text>
        </View>
        <Ionicons
          name="chevron-down"
          size={20}
          color={disabled ? Colors.neutral.warmGray : Colors.neutral.warmGrayDark}
        />
      </TouchableOpacity>

      {/* Country Selector Modal */}
      <Modal
        visible={isVisible}
        transparent={true}
        animationType="none"
        onRequestClose={closeSelector}
      >
        <View style={styles.modalOverlay}>
          <TouchableOpacity 
            style={styles.modalBackground} 
            onPress={closeSelector}
            activeOpacity={1}
          />
          
          <Animated.View 
            style={[
              styles.modalContent,
              { transform: [{ translateY: slideAnim }] }
            ]}
          >
            {/* Modal Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Country</Text>
              <TouchableOpacity onPress={closeSelector}>
                <Ionicons name="close" size={24} color={Colors.neutral.charcoal} />
              </TouchableOpacity>
            </View>

            {/* Countries List */}
            <FlatList
              data={countries}
              renderItem={renderCountryItem}
              keyExtractor={(item) => item.countryCode}
              style={styles.countriesList}
              showsVerticalScrollIndicator={true}
              contentContainerStyle={styles.countriesListContent}
            />
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 15,
  },
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    borderWidth: 1,
    borderColor: Colors.neutral.warmGrayLight,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 50,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 1,
  },
  disabledButton: {
    opacity: 0.6,
  },
  selectedCountryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  selectedFlag: {
    fontSize: 20,
    marginRight: 10,
  },
  selectedCountryName: {
    fontSize: 16,
    color: Colors.neutral.charcoal,
    fontWeight: '500',
    flex: 1,
  },
  selectedCountryCode: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '600',
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: Colors.overlay.medium,
    justifyContent: 'flex-end',
  },
  modalBackground: {
    flex: 1,
  },
  modalContent: {
    backgroundColor: Colors.neutral.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    height: screenHeight * 0.75,
    paddingBottom: 20,
    shadowColor: Colors.shadow.dark,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.warmGrayLight,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  countriesList: {
    flex: 1,
  },
  countriesListContent: {
    paddingBottom: 20,
  },
  countryItem: {
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  selectedCountryItem: {
    backgroundColor: Colors.primary.light + '20', // 20% opacity
    borderBottomColor: Colors.primary.light + '40', // 40% opacity
  },
  countryItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  countryFlag: {
    fontSize: 28,
    marginRight: 16,
    width: 40,
    textAlign: 'center',
  },
  countryInfo: {
    flex: 1,
  },
  countryName: {
    fontSize: 16,
    color: Colors.neutral.charcoal,
    fontWeight: '600',
    marginBottom: 2,
  },
  countryCodeText: {
    fontSize: 14,
    color: Colors.primary.main,
    fontWeight: '500',
  },
});

export default CountrySelector;
