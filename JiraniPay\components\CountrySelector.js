import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  FlatList,
  TextInput,
  Animated,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { getCountriesArray, getCountryByCode } from '../utils/countriesConfig';

const { height: screenHeight } = Dimensions.get('window');

/**
 * Reusable Country Selector Component
 * Provides dropdown/picker functionality for East African countries
 * 
 * @param {Object} props - Component props
 * @param {string} props.selectedCountryCode - Currently selected country code (e.g., 'UG')
 * @param {Function} props.onCountrySelect - Callback when country is selected
 * @param {Object} props.style - Additional styles for the container
 * @param {boolean} props.disabled - Whether the selector is disabled
 */
const CountrySelector = ({ 
  selectedCountryCode = 'UG', 
  onCountrySelect, 
  style = {},
  disabled = false 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [countries] = useState(getCountriesArray());
  const slideAnim = useRef(new Animated.Value(screenHeight)).current;

  const selectedCountry = getCountryByCode(selectedCountryCode);

  /**
   * Filters countries based on search query
   * @returns {Array} - Filtered countries array
   */
  const getFilteredCountries = () => {
    if (!searchQuery.trim()) return countries;
    
    return countries.filter(country =>
      country.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      country.code.includes(searchQuery)
    );
  };

  /**
   * Opens the country selector modal with animation
   */
  const openSelector = () => {
    if (disabled) return;
    
    setIsVisible(true);
    setSearchQuery('');
    
    // Animate modal slide up
    Animated.timing(slideAnim, {
      toValue: 0,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  /**
   * Closes the country selector modal with animation
   */
  const closeSelector = () => {
    Animated.timing(slideAnim, {
      toValue: screenHeight,
      duration: 250,
      useNativeDriver: true,
    }).start(() => {
      setIsVisible(false);
      setSearchQuery('');
    });
  };

  /**
   * Handles country selection
   * @param {Object} country - Selected country object
   */
  const handleCountrySelect = (country) => {
    onCountrySelect && onCountrySelect(country.countryCode, country);
    closeSelector();
  };

  /**
   * Renders individual country item in the list
   * @param {Object} item - Country object
   * @returns {JSX.Element} - Country item component
   */
  const renderCountryItem = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.countryItem,
        selectedCountryCode === item.countryCode && styles.selectedCountryItem
      ]}
      onPress={() => handleCountrySelect(item)}
    >
      <View style={styles.countryItemContent}>
        <Text style={styles.countryFlag}>{item.flag}</Text>
        <View style={styles.countryInfo}>
          <Text style={styles.countryName}>{item.name}</Text>
          <Text style={styles.countryCode}>{item.code}</Text>
        </View>
        {selectedCountryCode === item.countryCode && (
          <Ionicons name="checkmark" size={20} color="#5B37B7" />
        )}
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, style]}>
      {/* Country Selector Button */}
      <TouchableOpacity
        style={[styles.selectorButton, disabled && styles.disabledButton]}
        onPress={openSelector}
        disabled={disabled}
      >
        <View style={styles.selectedCountryContent}>
          <Text style={styles.selectedFlag}>{selectedCountry?.flag}</Text>
          <Text style={styles.selectedCountryName}>{selectedCountry?.name}</Text>
          <Text style={styles.selectedCountryCode}>{selectedCountry?.code}</Text>
        </View>
        <Ionicons 
          name="chevron-down" 
          size={20} 
          color={disabled ? "#999" : "#666"} 
        />
      </TouchableOpacity>

      {/* Country Selector Modal */}
      <Modal
        visible={isVisible}
        transparent={true}
        animationType="none"
        onRequestClose={closeSelector}
      >
        <View style={styles.modalOverlay}>
          <TouchableOpacity 
            style={styles.modalBackground} 
            onPress={closeSelector}
            activeOpacity={1}
          />
          
          <Animated.View 
            style={[
              styles.modalContent,
              { transform: [{ translateY: slideAnim }] }
            ]}
          >
            {/* Modal Header */}
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Country</Text>
              <TouchableOpacity onPress={closeSelector}>
                <Ionicons name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            {/* Search Input */}
            <View style={styles.searchContainer}>
              <Ionicons name="search" size={20} color="#666" style={styles.searchIcon} />
              <TextInput
                style={styles.searchInput}
                placeholder="Search countries..."
                value={searchQuery}
                onChangeText={setSearchQuery}
                autoCapitalize="none"
                autoCorrect={false}
              />
            </View>

            {/* Countries List */}
            <FlatList
              data={getFilteredCountries()}
              renderItem={renderCountryItem}
              keyExtractor={(item) => item.countryCode}
              style={styles.countriesList}
              showsVerticalScrollIndicator={false}
              keyboardShouldPersistTaps="handled"
            />
          </Animated.View>
        </View>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 15,
  },
  selectorButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#F8F9FA',
    borderWidth: 1,
    borderColor: '#E0E0E0',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 50,
  },
  disabledButton: {
    opacity: 0.6,
  },
  selectedCountryContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  selectedFlag: {
    fontSize: 20,
    marginRight: 10,
  },
  selectedCountryName: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
    flex: 1,
  },
  selectedCountryCode: {
    fontSize: 14,
    color: '#5B37B7',
    fontWeight: '600',
    marginLeft: 8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalBackground: {
    flex: 1,
  },
  modalContent: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: screenHeight * 0.8,
    paddingBottom: 20,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    marginHorizontal: 20,
    marginVertical: 16,
    paddingHorizontal: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
    paddingVertical: 12,
  },
  countriesList: {
    flex: 1,
  },
  countryItem: {
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  selectedCountryItem: {
    backgroundColor: '#F0F0FF',
  },
  countryItemContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  countryFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  countryInfo: {
    flex: 1,
  },
  countryName: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  countryCode: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
});

export default CountrySelector;
