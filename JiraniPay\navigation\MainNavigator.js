import React, { useState } from 'react';
import { View, Text, StyleSheet, StatusBar } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import DashboardScreen from '../screens/DashboardScreen';
import ProfileScreen from '../screens/ProfileScreen';
import BottomNavigation from '../components/BottomNavigation';
import { Colors } from '../constants/Colors';

// Enhanced placeholder screens for other tabs
const BillsScreen = () => (
  <View style={styles.placeholderScreen}>
    <Ionicons name="receipt" size={64} color={Colors.primary.main} />
    <Text style={styles.placeholderText}>Bills & Payments</Text>
    <Text style={styles.placeholderSubtext}>Pay utilities, electricity, water, and TV subscriptions</Text>
    <Text style={styles.comingSoonBadge}>Coming Soon</Text>
  </View>
);

const QRPayScreen = () => (
  <View style={styles.placeholderScreen}>
    <Ionicons name="qr-code" size={64} color={Colors.primary.main} />
    <Text style={styles.placeholderText}>QR Pay & Scan</Text>
    <Text style={styles.placeholderSubtext}>Scan QR codes to pay merchants instantly</Text>
    <Text style={styles.comingSoonBadge}>Coming Soon</Text>
  </View>
);

const WalletScreen = () => (
  <View style={styles.placeholderScreen}>
    <Ionicons name="wallet" size={64} color={Colors.primary.main} />
    <Text style={styles.placeholderText}>Wallet Management</Text>
    <Text style={styles.placeholderSubtext}>Manage your accounts, cards, and payment methods</Text>
    <Text style={styles.comingSoonBadge}>Coming Soon</Text>
  </View>
);

// ProfileScreen is now imported from ../screens/ProfileScreen

const MainNavigator = ({ navigation }) => {
  const [activeTab, setActiveTab] = useState('home');

  const renderScreen = () => {
    switch (activeTab) {
      case 'home':
        return <DashboardScreen navigation={navigation} />;
      case 'bills':
        return <BillsScreen />;
      case 'qr':
        return <QRPayScreen />;
      case 'wallet':
        return <WalletScreen />;
      case 'profile':
        return <ProfileScreen navigation={navigation} />;
      default:
        return <DashboardScreen navigation={navigation} />;
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={Colors.neutral.cream}
        translucent={false}
      />
      {renderScreen()}
      <BottomNavigation
        activeTab={activeTab}
        onTabPress={setActiveTab}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
  },
  placeholderScreen: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.neutral.cream,
    paddingBottom: 100, // Account for bottom navigation
  },
  placeholderText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  placeholderSubtext: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    marginHorizontal: 40,
    lineHeight: 22,
    marginBottom: 20,
  },
  comingSoonBadge: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.primary.main,
    backgroundColor: Colors.primary.light + '20',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    overflow: 'hidden',
  },
});

export default MainNavigator;
