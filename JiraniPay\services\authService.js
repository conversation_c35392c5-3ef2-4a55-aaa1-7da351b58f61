import supabase from './supabaseClient';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';

class AuthService {
  constructor() {
    this.currentUser = null;
    this.authStateListeners = [];
  }

  // Initialize auth state listener
  initialize() {
    supabase.auth.onAuthStateChange((event, session) => {
      this.currentUser = session?.user || null;
      this.notifyAuthStateListeners(event, session);
    });
  }

  // Add auth state listener
  addAuthStateListener(listener) {
    this.authStateListeners.push(listener);
  }

  // Remove auth state listener
  removeAuthStateListener(listener) {
    this.authStateListeners = this.authStateListeners.filter(l => l !== listener);
  }

  // Notify all listeners of auth state changes
  notifyAuthStateListeners(event, session) {
    this.authStateListeners.forEach(listener => listener(event, session));
  }

  // Send OTP to phone number
  async sendOTP(phoneNumber) {
    try {
      // Format phone number to international format
      const formattedPhone = this.formatPhoneNumber(phoneNumber);
      
      const { data, error } = await supabase.auth.signInWithOtp({
        phone: formattedPhone,
      });

      if (error) throw error;
      
      return { success: true, data };
    } catch (error) {
      console.error('Error sending OTP:', error);
      return { success: false, error: error.message };
    }
  }

  // Verify OTP and sign in
  async verifyOTP(phoneNumber, otp) {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber);
      
      const { data, error } = await supabase.auth.verifyOtp({
        phone: formattedPhone,
        token: otp,
        type: 'sms'
      });

      if (error) throw error;

      // Store user session securely
      if (data.session) {
        await this.storeSession(data.session);
      }

      return { success: true, data };
    } catch (error) {
      console.error('Error verifying OTP:', error);
      return { success: false, error: error.message };
    }
  }

  // Register new user with additional profile data
  async registerUser(phoneNumber, otp, profileData) {
    try {
      // For now, just verify OTP and return success
      // We'll implement profile creation when database is set up
      const verifyResult = await this.verifyOTP(phoneNumber, otp);

      if (!verifyResult.success) {
        return verifyResult;
      }

      // TODO: Create user profile in database when Supabase is configured
      console.log('Profile data to save:', profileData);

      return { success: true, data: verifyResult.data };
    } catch (error) {
      console.error('Error registering user:', error);
      return { success: false, error: error.message };
    }
  }

  // Check if biometric authentication is available
  async isBiometricAvailable() {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      
      return {
        available: hasHardware && isEnrolled,
        types: supportedTypes,
      };
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      return { available: false, types: [] };
    }
  }

  // Enable biometric authentication for user
  async enableBiometric() {
    try {
      const biometricCheck = await this.isBiometricAvailable();
      
      if (!biometricCheck.available) {
        throw new Error('Biometric authentication not available');
      }

      // Store biometric preference
      await SecureStore.setItemAsync('biometric_enabled', 'true');
      
      return { success: true };
    } catch (error) {
      console.error('Error enabling biometric:', error);
      return { success: false, error: error.message };
    }
  }

  // Authenticate with biometrics
  async authenticateWithBiometric() {
    try {
      const biometricEnabled = await SecureStore.getItemAsync('biometric_enabled');
      
      if (biometricEnabled !== 'true') {
        throw new Error('Biometric authentication not enabled');
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to access your account',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use PIN',
      });

      if (result.success) {
        // Get stored session and restore it
        const storedSession = await this.getStoredSession();
        if (storedSession) {
          await supabase.auth.setSession(storedSession);
          return { success: true };
        }
      }

      return { success: false, error: 'Authentication failed' };
    } catch (error) {
      console.error('Error with biometric authentication:', error);
      return { success: false, error: error.message };
    }
  }

  // Store session securely
  async storeSession(session) {
    try {
      await SecureStore.setItemAsync('user_session', JSON.stringify(session));
    } catch (error) {
      console.error('Error storing session:', error);
    }
  }

  // Get stored session
  async getStoredSession() {
    try {
      const sessionString = await SecureStore.getItemAsync('user_session');
      return sessionString ? JSON.parse(sessionString) : null;
    } catch (error) {
      console.error('Error getting stored session:', error);
      return null;
    }
  }

  // Sign out user
  async signOut() {
    try {
      await supabase.auth.signOut();
      await SecureStore.deleteItemAsync('user_session');
      await SecureStore.deleteItemAsync('biometric_enabled');
      
      return { success: true };
    } catch (error) {
      console.error('Error signing out:', error);
      return { success: false, error: error.message };
    }
  }

  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }

  // Get user profile
  async getUserProfile(userId) {
    try {
      // TODO: Implement when database is set up
      console.log('Getting profile for user:', userId);

      // For now, return that profile doesn't exist (new user)
      return { success: false, error: 'Profile not found' };
    } catch (error) {
      console.error('Error getting user profile:', error);
      return { success: false, error: error.message };
    }
  }

  // Format phone number to international format
  formatPhoneNumber(phoneNumber) {
    // Remove all non-digit characters
    const cleaned = phoneNumber.replace(/\D/g, '');
    
    // Add Uganda country code if not present
    if (cleaned.startsWith('0')) {
      return '+256' + cleaned.substring(1);
    } else if (cleaned.startsWith('256')) {
      return '+' + cleaned;
    } else if (cleaned.startsWith('+256')) {
      return cleaned;
    } else {
      // Assume it's a local number and add Uganda code
      return '+256' + cleaned;
    }
  }

  // Detect mobile network provider
  detectNetworkProvider(phoneNumber) {
    const cleaned = phoneNumber.replace(/\D/g, '');
    const localNumber = cleaned.startsWith('256') ? cleaned.substring(3) : 
                       cleaned.startsWith('0') ? cleaned.substring(1) : cleaned;

    // MTN Uganda prefixes
    const mtnPrefixes = ['77', '78', '76', '39'];
    // Airtel Uganda prefixes  
    const airtelPrefixes = ['75', '70', '74', '20'];
    // UTL prefixes
    const utlPrefixes = ['71', '31'];

    for (const prefix of mtnPrefixes) {
      if (localNumber.startsWith(prefix)) {
        return 'MTN';
      }
    }

    for (const prefix of airtelPrefixes) {
      if (localNumber.startsWith(prefix)) {
        return 'Airtel';
      }
    }

    for (const prefix of utlPrefixes) {
      if (localNumber.startsWith(prefix)) {
        return 'UTL';
      }
    }

    return 'Unknown';
  }
}

// Create and export a singleton instance
const authService = new AuthService();
export default authService;
