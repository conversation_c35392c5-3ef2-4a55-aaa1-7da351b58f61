import supabase from './supabaseClient';
import databaseService from './databaseService';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import {
  detectNetworkProvider as detectProvider,
  formatPhoneNumber as formatPhone,
  validatePhoneNumber,
  getCountryByCode
} from '../utils/countriesConfig';

class AuthService {
  constructor() {
    this.currentUser = null;
    this.authStateListeners = [];
  }

  // Initialize auth state listener
  initialize() {
    supabase.auth.onAuthStateChange((event, session) => {
      this.currentUser = session?.user || null;
      this.notifyAuthStateListeners(event, session);
    });
  }

  // Add auth state listener
  addAuthStateListener(listener) {
    this.authStateListeners.push(listener);
  }

  // Remove auth state listener
  removeAuthStateListener(listener) {
    this.authStateListeners = this.authStateListeners.filter(l => l !== listener);
  }

  // Notify all listeners of auth state changes
  notifyAuthStateListeners(event, session) {
    this.authStateListeners.forEach(listener => listener(event, session));
  }

  // Send OTP to phone number
  async sendOTP(phoneNumber, countryCode = 'UG') {
    try {
      // Format phone number to international format
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      console.log('📱 Sending OTP to:', formattedPhone);

      const { data, error } = await supabase.auth.signInWithOtp({
        phone: formattedPhone,
      });

      if (error) throw error;

      console.log('✅ OTP sent successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error sending OTP:', error);
      return { success: false, error: error.message };
    }
  }

  // Verify OTP and sign in
  async verifyOTP(phoneNumber, otp, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      // Development mode - simulate OTP verification
      if (this.isDevelopmentMode) {
        console.log('🔐 [DEV MODE] Verifying OTP for:', formattedPhone);
        console.log('🔐 [DEV MODE] OTP entered:', otp);

        // Accept any 6-digit code in development mode
        if (otp && otp.length === 6) {
          // Simulate network delay
          await new Promise(resolve => setTimeout(resolve, 800));

          // Create mock user session
          const mockSession = {
            access_token: 'mock-access-token',
            refresh_token: 'mock-refresh-token',
            user: {
              id: 'mock-user-' + Date.now(),
              phone: formattedPhone,
              created_at: new Date().toISOString()
            }
          };

          // Store mock session
          await this.storeSession(mockSession);
          this.currentUser = mockSession.user;

          console.log('✅ [DEV MODE] OTP verification successful');
          return { success: true, data: { session: mockSession, user: mockSession.user } };
        } else {
          throw new Error('Please enter a valid 6-digit OTP');
        }
      }

      // Production mode - use Supabase
      const { data, error } = await supabase.auth.verifyOtp({
        phone: formattedPhone,
        token: otp,
        type: 'sms'
      });

      if (error) throw error;

      // Store user session securely
      if (data.session) {
        await this.storeSession(data.session);
      }

      return { success: true, data };
    } catch (error) {
      console.error('Error verifying OTP:', error);
      return { success: false, error: error.message };
    }
  }

  // Register new user with password
  async registerUserWithPassword(phoneNumber, password, profileData, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      // Development mode - simulate registration
      if (this.isDevelopmentMode) {
        console.log('👤 [DEV MODE] Registering user with password:', {
          phone: formattedPhone,
          profileData,
          country: countryCode
        });

        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1200));

        // Create mock user with session
        const mockUser = {
          id: 'mock-user-' + Date.now(),
          phone: formattedPhone,
          email: profileData.email || null,
          full_name: profileData.fullName || null,
          country: countryCode,
          created_at: new Date().toISOString()
        };

        // Create mock session
        const mockSession = {
          access_token: 'mock-access-token',
          refresh_token: 'mock-refresh-token',
          user: mockUser
        };

        // Store session and set current user
        await this.storeSession(mockSession);
        this.currentUser = mockUser;

        console.log('✅ [DEV MODE] User registration successful');
        return { success: true, data: { user: mockUser, session: mockSession } };
      }

      // Production mode - implement with Supabase when configured
      console.log('Registering user with password:', {
        phone: formattedPhone,
        profileData,
        country: countryCode
      });

      // Simulate user creation for production (to be replaced with real Supabase implementation)
      const mockUser = {
        id: 'mock-user-id',
        phone: formattedPhone,
        email: profileData.email || null,
        country: countryCode
      };

      return { success: true, data: { user: mockUser } };
    } catch (error) {
      console.error('Error registering user with password:', error);
      return { success: false, error: error.message };
    }
  }

  // Login with password
  async loginWithPassword(phoneNumber, password, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      // Development mode - simulate login
      if (this.isDevelopmentMode) {
        console.log('🔑 [DEV MODE] Logging in with password:', formattedPhone);

        // Simulate password validation
        if (password.length < 6) {
          throw new Error('Invalid credentials');
        }

        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Create mock user with session
        const mockUser = {
          id: 'mock-user-' + Date.now(),
          phone: formattedPhone,
          country: countryCode,
          full_name: 'Demo User',
          created_at: new Date().toISOString()
        };

        // Create mock session
        const mockSession = {
          access_token: 'mock-access-token',
          refresh_token: 'mock-refresh-token',
          user: mockUser
        };

        // Store session and set current user
        await this.storeSession(mockSession);
        this.currentUser = mockUser;

        console.log('✅ [DEV MODE] Login successful');
        return { success: true, data: { user: mockUser, session: mockSession } };
      }

      // Production mode - implement with Supabase when configured
      console.log('Logging in with password:', formattedPhone);

      // Simulate password validation
      if (password.length < 6) {
        throw new Error('Invalid credentials');
      }

      // Simulate successful login
      const mockUser = {
        id: 'mock-user-id',
        phone: formattedPhone,
        country: countryCode
      };

      return { success: true, data: { user: mockUser } };
    } catch (error) {
      console.error('Error logging in with password:', error);
      return { success: false, error: error.message };
    }
  }

  // Forgot password - reset password with new password
  async forgotPassword(phoneNumber, newPassword, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      // TODO: Implement with Supabase when configured
      // For now, simulate password reset
      console.log('Resetting password for:', formattedPhone);

      // Simulate password validation
      if (newPassword.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }

      // Simulate successful password reset
      return { success: true, message: 'Password reset successfully' };
    } catch (error) {
      console.error('Error resetting password:', error);
      return { success: false, error: error.message };
    }
  }

  // Register new user with additional profile data (OTP method)
  async registerUser(phoneNumber, otp, profileData) {
    try {
      // For now, just verify OTP and return success
      // We'll implement profile creation when database is set up
      const verifyResult = await this.verifyOTP(phoneNumber, otp);

      if (!verifyResult.success) {
        return verifyResult;
      }

      // TODO: Create user profile in database when Supabase is configured
      console.log('Profile data to save:', profileData);

      return { success: true, data: verifyResult.data };
    } catch (error) {
      console.error('Error registering user:', error);
      return { success: false, error: error.message };
    }
  }

  // Check if biometric authentication is available
  async isBiometricAvailable() {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      
      return {
        available: hasHardware && isEnrolled,
        types: supportedTypes,
      };
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      return { available: false, types: [] };
    }
  }

  // Enable biometric authentication for user
  async enableBiometric() {
    try {
      const biometricCheck = await this.isBiometricAvailable();
      
      if (!biometricCheck.available) {
        throw new Error('Biometric authentication not available');
      }

      // Store biometric preference
      await SecureStore.setItemAsync('biometric_enabled', 'true');
      
      return { success: true };
    } catch (error) {
      console.error('Error enabling biometric:', error);
      return { success: false, error: error.message };
    }
  }

  // Authenticate with biometrics
  async authenticateWithBiometric() {
    try {
      const biometricEnabled = await SecureStore.getItemAsync('biometric_enabled');
      
      if (biometricEnabled !== 'true') {
        throw new Error('Biometric authentication not enabled');
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to access your account',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use PIN',
      });

      if (result.success) {
        // Get stored session and restore it
        const storedSession = await this.getStoredSession();
        if (storedSession) {
          await supabase.auth.setSession(storedSession);
          return { success: true };
        }
      }

      return { success: false, error: 'Authentication failed' };
    } catch (error) {
      console.error('Error with biometric authentication:', error);
      return { success: false, error: error.message };
    }
  }

  // Store session securely
  async storeSession(session) {
    try {
      await SecureStore.setItemAsync('user_session', JSON.stringify(session));
    } catch (error) {
      console.error('Error storing session:', error);
    }
  }

  // Get stored session
  async getStoredSession() {
    try {
      const sessionString = await SecureStore.getItemAsync('user_session');
      return sessionString ? JSON.parse(sessionString) : null;
    } catch (error) {
      console.error('Error getting stored session:', error);
      return null;
    }
  }

  // Sign out user
  async signOut() {
    try {
      await supabase.auth.signOut();
      await SecureStore.deleteItemAsync('user_session');
      await SecureStore.deleteItemAsync('biometric_enabled');
      
      return { success: true };
    } catch (error) {
      console.error('Error signing out:', error);
      return { success: false, error: error.message };
    }
  }

  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }

  // Get user profile
  async getUserProfile(userId) {
    try {
      // TODO: Implement when database is set up
      console.log('Getting profile for user:', userId);

      // For now, return that profile doesn't exist (new user)
      return { success: false, error: 'Profile not found' };
    } catch (error) {
      console.error('Error getting user profile:', error);
      return { success: false, error: error.message };
    }
  }

  // Format phone number to international format for any East African country
  formatPhoneNumber(phoneNumber, countryCode = 'UG') {
    return formatPhone(phoneNumber, countryCode);
  }

  // Detect mobile network provider for any East African country
  detectNetworkProvider(phoneNumber, countryCode = 'UG') {
    const provider = detectProvider(phoneNumber, countryCode);
    return provider ? provider.name : 'Unknown';
  }

  // Validate phone number for any East African country
  validatePhoneNumber(phoneNumber, countryCode = 'UG') {
    return validatePhoneNumber(phoneNumber, countryCode);
  }

  // Get country configuration
  getCountryConfig(countryCode) {
    return getCountryByCode(countryCode);
  }
}

// Create and export a singleton instance
const authService = new AuthService();
export default authService;
