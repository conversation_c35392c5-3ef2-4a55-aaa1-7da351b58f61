import supabase from './supabaseClient';
import databaseService from './databaseService';
import * as LocalAuthentication from 'expo-local-authentication';
import * as SecureStore from 'expo-secure-store';
import {
  detectNetworkProvider as detectProvider,
  formatPhoneNumber as formatPhone,
  validatePhoneNumber,
  getCountryByCode
} from '../utils/countriesConfig';

class AuthService {
  constructor() {
    this.currentUser = null;
    this.authStateListeners = [];

    // Initialize auth state listener
    this.initialize();
  }

  // Initialize authentication state listener
  initialize() {
    console.log('🔧 Initializing auth service...');

    supabase.auth.onAuthStateChange((event, session) => {
      console.log('🔐 Auth state changed:', event, session ? 'with session' : 'no session');

      if (session?.user) {
        this.currentUser = session.user;
        console.log('✅ User authenticated:', session.user.id);
      } else {
        this.currentUser = null;
        console.log('ℹ️ User signed out');
      }

      // Notify listeners
      this.authStateListeners.forEach(listener => {
        listener(this.currentUser);
      });
    });
  }

  // Add auth state listener
  addAuthStateListener(listener) {
    this.authStateListeners.push(listener);
  }

  // Remove auth state listener
  removeAuthStateListener(listener) {
    this.authStateListeners = this.authStateListeners.filter(l => l !== listener);
  }

  // Notify all listeners of auth state changes
  notifyAuthStateListeners(event, session) {
    this.authStateListeners.forEach(listener => listener(event, session));
  }

  // Send OTP to phone number
  async sendOTP(phoneNumber, countryCode = 'UG') {
    try {
      // Format phone number to international format
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      console.log('📱 Sending OTP to:', formattedPhone);

      // 🚀 DEVELOPMENT MODE: Skip actual SMS and simulate OTP sending
      console.log('🔧 Development Mode: SMS provider not configured');
      console.log('💡 Use OTP: 123456 for any phone number');

      // Store the phone number for verification
      await SecureStore.setItemAsync('pending_phone', formattedPhone);
      await SecureStore.setItemAsync('dev_otp', '123456');

      console.log('✅ Development OTP ready (123456)');
      return {
        success: true,
        data: {
          phone: formattedPhone,
          message: 'Development mode: Use OTP 123456'
        }
      };

      // 📱 PRODUCTION CODE (uncomment when SMS is configured):
      // const { data, error } = await supabase.auth.signInWithOtp({
      //   phone: formattedPhone,
      // });
      // if (error) throw error;
      // return { success: true, data };

    } catch (error) {
      console.error('❌ Error sending OTP:', error);
      return { success: false, error: error.message };
    }
  }

  // Verify OTP and sign in
  async verifyOTP(phoneNumber, otp, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      console.log('🔐 Verifying OTP for:', formattedPhone);

      // 🚀 DEVELOPMENT MODE: Check against stored development OTP
      const pendingPhone = await SecureStore.getItemAsync('pending_phone');
      const devOtp = await SecureStore.getItemAsync('dev_otp');

      if (pendingPhone === formattedPhone && otp === devOtp) {
        console.log('✅ Development OTP verified successfully');

        // Create a mock user session for development
        // Generate a UUID-like format for development
        const generateDevUUID = () => {
          return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
          });
        };

        const mockUser = {
          id: generateDevUUID(),
          phone: formattedPhone,
          email: `${phoneNumber}@jiranipay.dev`,
          user_metadata: {
            full_name: 'Development User',
            phone: formattedPhone
          },
          created_at: new Date().toISOString()
        };

        const mockSession = {
          access_token: 'dev_access_token',
          refresh_token: 'dev_refresh_token',
          expires_in: 3600,
          user: mockUser
        };

        // Store mock session
        await this.storeSession(mockSession);
        this.currentUser = mockUser;

        // Manually notify auth state listeners for development mode
        this.authStateListeners.forEach(listener => {
          listener(mockUser);
        });

        // Clean up development OTP
        await SecureStore.deleteItemAsync('pending_phone');
        await SecureStore.deleteItemAsync('dev_otp');

        return {
          success: true,
          data: {
            user: mockUser,
            session: mockSession
          }
        };
      }

      // 📱 PRODUCTION CODE (uncomment when SMS is configured):
      // const { data, error } = await supabase.auth.verifyOtp({
      //   phone: formattedPhone,
      //   token: otp,
      //   type: 'sms'
      // });
      // if (error) throw error;
      // if (data.session) {
      //   await this.storeSession(data.session);
      //   this.currentUser = data.user;
      // }
      // return { success: true, data };

      // If we reach here, OTP is invalid
      throw new Error('Invalid OTP. Use 123456 for development.');

    } catch (error) {
      console.error('❌ Error verifying OTP:', error);
      return { success: false, error: error.message };
    }
  }

  // Register new user with password
  async registerUserWithPassword(phoneNumber, password, profileData, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      console.log('👤 Registering user with password:', formattedPhone);

      // Create user account with Supabase Auth
      const { data: authData, error: authError } = await supabase.auth.signUp({
        phone: formattedPhone,
        password: password,
      });

      if (authError) throw authError;

      if (authData.user) {
        // Create user profile in database
        const profileResult = await databaseService.createUserProfile({
          userId: authData.user.id,
          fullName: profileData.fullName,
          phoneNumber: formattedPhone,
          countryCode: countryCode,
          preferredLanguage: profileData.preferredLanguage || 'en',
        });

        if (!profileResult.success) {
          console.warn('⚠️ Profile creation failed, but user account created');
        }

        // Store session if available
        if (authData.session) {
          await this.storeSession(authData.session);
          this.currentUser = authData.user;
        }

        console.log('✅ User registered successfully');
        return { success: true, data: authData };
      }

      throw new Error('User creation failed');
    } catch (error) {
      console.error('❌ Error registering user with password:', error);
      return { success: false, error: error.message };
    }
  }

  // Login with password
  async loginWithPassword(phoneNumber, password, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      console.log('🔑 Logging in with password:', formattedPhone);

      // 🚀 DEVELOPMENT MODE: Accept any password for testing
      if (password.length >= 6) {
        console.log('✅ Development password login successful');

        // Create a mock user session for development
        // Generate a UUID-like format for development
        const generateDevUUID = () => {
          return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
          });
        };

        const mockUser = {
          id: generateDevUUID(),
          phone: formattedPhone,
          email: `${phoneNumber}@jiranipay.dev`,
          user_metadata: {
            full_name: 'Development User',
            phone: formattedPhone
          },
          created_at: new Date().toISOString()
        };

        const mockSession = {
          access_token: 'dev_access_token',
          refresh_token: 'dev_refresh_token',
          expires_in: 3600,
          user: mockUser
        };

        // Store mock session
        await this.storeSession(mockSession);
        this.currentUser = mockUser;

        // Manually notify auth state listeners for development mode
        this.authStateListeners.forEach(listener => {
          listener(mockUser);
        });

        return {
          success: true,
          data: {
            user: mockUser,
            session: mockSession
          }
        };
      }

      // 📱 PRODUCTION CODE (uncomment when ready):
      // const { data, error } = await supabase.auth.signInWithPassword({
      //   phone: formattedPhone,
      //   password: password,
      // });
      // if (error) throw error;
      // if (data.session) {
      //   await this.storeSession(data.session);
      //   this.currentUser = data.user;
      // }
      // return { success: true, data };

      throw new Error('Password must be at least 6 characters');

    } catch (error) {
      console.error('❌ Error logging in with password:', error);
      return { success: false, error: error.message };
    }
  }

  // Forgot password - reset password with new password
  async forgotPassword(phoneNumber, newPassword, countryCode = 'UG') {
    try {
      const formattedPhone = this.formatPhoneNumber(phoneNumber, countryCode);

      // TODO: Implement with Supabase when configured
      // For now, simulate password reset
      console.log('Resetting password for:', formattedPhone);

      // Simulate password validation
      if (newPassword.length < 8) {
        throw new Error('Password must be at least 8 characters long');
      }

      // Simulate successful password reset
      return { success: true, message: 'Password reset successfully' };
    } catch (error) {
      console.error('Error resetting password:', error);
      return { success: false, error: error.message };
    }
  }

  // Create user profile in database
  async createUserProfile(userId, profileData) {
    try {
      const formattedPhone = this.formatPhoneNumber(profileData.phoneNumber, profileData.countryCode);

      const result = await databaseService.createUserProfile({
        userId: userId,
        fullName: profileData.fullName,
        phoneNumber: formattedPhone,
        countryCode: profileData.countryCode,
        preferredLanguage: profileData.preferredLanguage || 'en',
      });

      console.log('✅ User profile created successfully');
      return result;
    } catch (error) {
      console.error('❌ Error creating user profile:', error);
      return { success: false, error: error.message };
    }
  }

  // Register new user with additional profile data (OTP method)
  async registerUser(phoneNumber, otp, profileData, countryCode = 'UG') {
    try {
      // First verify OTP
      const verifyResult = await this.verifyOTP(phoneNumber, otp, countryCode);

      if (!verifyResult.success) {
        return verifyResult;
      }

      // Create user profile in database if user was created
      if (verifyResult.data?.user) {
        const profileResult = await this.createUserProfile(verifyResult.data.user.id, {
          fullName: profileData.fullName,
          phoneNumber: phoneNumber,
          countryCode: countryCode,
          preferredLanguage: profileData.preferredLanguage || 'en',
        });

        if (!profileResult.success) {
          console.warn('⚠️ Profile creation failed, but user account exists');
        }
      }

      console.log('✅ User registration with OTP successful');
      return { success: true, data: verifyResult.data };
    } catch (error) {
      console.error('❌ Error registering user:', error);
      return { success: false, error: error.message };
    }
  }

  // Check if biometric authentication is available
  async isBiometricAvailable() {
    try {
      const hasHardware = await LocalAuthentication.hasHardwareAsync();
      const isEnrolled = await LocalAuthentication.isEnrolledAsync();
      const supportedTypes = await LocalAuthentication.supportedAuthenticationTypesAsync();
      
      return {
        available: hasHardware && isEnrolled,
        types: supportedTypes,
      };
    } catch (error) {
      console.error('Error checking biometric availability:', error);
      return { available: false, types: [] };
    }
  }

  // Enable biometric authentication for user
  async enableBiometric() {
    try {
      const biometricCheck = await this.isBiometricAvailable();
      
      if (!biometricCheck.available) {
        throw new Error('Biometric authentication not available');
      }

      // Store biometric preference
      await SecureStore.setItemAsync('biometric_enabled', 'true');
      
      return { success: true };
    } catch (error) {
      console.error('Error enabling biometric:', error);
      return { success: false, error: error.message };
    }
  }

  // Authenticate with biometrics
  async authenticateWithBiometric() {
    try {
      const biometricEnabled = await SecureStore.getItemAsync('biometric_enabled');
      
      if (biometricEnabled !== 'true') {
        throw new Error('Biometric authentication not enabled');
      }

      const result = await LocalAuthentication.authenticateAsync({
        promptMessage: 'Authenticate to access your account',
        cancelLabel: 'Cancel',
        fallbackLabel: 'Use PIN',
      });

      if (result.success) {
        // Get stored session and restore it
        const storedSession = await this.getStoredSession();
        if (storedSession) {
          await supabase.auth.setSession(storedSession);
          return { success: true };
        }
      }

      return { success: false, error: 'Authentication failed' };
    } catch (error) {
      console.error('Error with biometric authentication:', error);
      return { success: false, error: error.message };
    }
  }

  // Store session securely
  async storeSession(session) {
    try {
      await SecureStore.setItemAsync('user_session', JSON.stringify(session));
    } catch (error) {
      console.error('Error storing session:', error);
    }
  }

  // Get stored session
  async getStoredSession() {
    try {
      const sessionString = await SecureStore.getItemAsync('user_session');
      return sessionString ? JSON.parse(sessionString) : null;
    } catch (error) {
      console.error('Error getting stored session:', error);
      return null;
    }
  }

  // Sign out user
  async signOut() {
    try {
      await supabase.auth.signOut();
      await SecureStore.deleteItemAsync('user_session');
      await SecureStore.deleteItemAsync('biometric_enabled');
      
      return { success: true };
    } catch (error) {
      console.error('Error signing out:', error);
      return { success: false, error: error.message };
    }
  }

  // Get current user
  getCurrentUser() {
    return this.currentUser;
  }

  // Get user profile
  async getUserProfile(userId) {
    try {
      console.log('📋 Getting profile for user:', userId);

      return await databaseService.getUserProfile(userId);
    } catch (error) {
      console.error('❌ Error getting user profile:', error);
      return { success: false, error: error.message };
    }
  }

  // Format phone number to international format for any East African country
  formatPhoneNumber(phoneNumber, countryCode = 'UG') {
    return formatPhone(phoneNumber, countryCode);
  }

  // Detect mobile network provider for any East African country
  detectNetworkProvider(phoneNumber, countryCode = 'UG') {
    const provider = detectProvider(phoneNumber, countryCode);
    return provider ? provider.name : 'Unknown';
  }

  // Validate phone number for any East African country
  validatePhoneNumber(phoneNumber, countryCode = 'UG') {
    return validatePhoneNumber(phoneNumber, countryCode);
  }

  // Get country configuration
  getCountryConfig(countryCode) {
    return getCountryByCode(countryCode);
  }
}

// Create and export a singleton instance
const authService = new AuthService();
export default authService;
