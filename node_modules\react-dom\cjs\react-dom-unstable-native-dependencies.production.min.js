/** @license React v16.13.1
 * react-dom-unstable-native-dependencies.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

'use strict';var h=require("react-dom"),k=require("object-assign");function m(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var n=null,q=null,r=null;
function t(a){var b=a._dispatchListeners,c=a._dispatchInstances;if(Array.isArray(b))throw Error(m(103));a.currentTarget=b?r(c):null;b=b?b(a):null;a.currentTarget=null;a._dispatchListeners=null;a._dispatchInstances=null;return b}function u(a){do a=a.return;while(a&&5!==a.tag);return a?a:null}function v(a,b,c){for(var f=[];a;)f.push(a),a=u(a);for(a=f.length;0<a--;)b(f[a],"captured",c);for(a=0;a<f.length;a++)b(f[a],"bubbled",c)}
function w(a,b){var c=a.stateNode;if(!c)return null;var f=n(c);if(!f)return null;c=f[b];a:switch(b){case "onClick":case "onClickCapture":case "onDoubleClick":case "onDoubleClickCapture":case "onMouseDown":case "onMouseDownCapture":case "onMouseMove":case "onMouseMoveCapture":case "onMouseUp":case "onMouseUpCapture":case "onMouseEnter":(f=!f.disabled)||(a=a.type,f=!("button"===a||"input"===a||"select"===a||"textarea"===a));a=!f;break a;default:a=!1}if(a)return null;if(c&&"function"!==typeof c)throw Error(m(231,
b,typeof c));return c}function x(a,b){if(null==b)throw Error(m(30));if(null==a)return b;if(Array.isArray(a)){if(Array.isArray(b))return a.push.apply(a,b),a;a.push(b);return a}return Array.isArray(b)?[a].concat(b):[a,b]}function y(a,b,c){Array.isArray(a)?a.forEach(b,c):a&&b.call(c,a)}function z(a,b,c){if(b=w(a,c.dispatchConfig.phasedRegistrationNames[b]))c._dispatchListeners=x(c._dispatchListeners,b),c._dispatchInstances=x(c._dispatchInstances,a)}
function A(a){a&&a.dispatchConfig.phasedRegistrationNames&&v(a._targetInst,z,a)}function aa(a){if(a&&a.dispatchConfig.phasedRegistrationNames){var b=a._targetInst;b=b?u(b):null;v(b,z,a)}}function B(a){if(a&&a.dispatchConfig.registrationName){var b=a._targetInst;if(b&&a&&a.dispatchConfig.registrationName){var c=w(b,a.dispatchConfig.registrationName);c&&(a._dispatchListeners=x(a._dispatchListeners,c),a._dispatchInstances=x(a._dispatchInstances,b))}}}function C(){return!0}function D(){return!1}
function E(a,b,c,f){this.dispatchConfig=a;this._targetInst=b;this.nativeEvent=c;a=this.constructor.Interface;for(var d in a)a.hasOwnProperty(d)&&((b=a[d])?this[d]=b(c):"target"===d?this.target=f:this[d]=c[d]);this.isDefaultPrevented=(null!=c.defaultPrevented?c.defaultPrevented:!1===c.returnValue)?C:D;this.isPropagationStopped=D;return this}
k(E.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():"unknown"!==typeof a.returnValue&&(a.returnValue=!1),this.isDefaultPrevented=C)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():"unknown"!==typeof a.cancelBubble&&(a.cancelBubble=!0),this.isPropagationStopped=C)},persist:function(){this.isPersistent=C},isPersistent:D,destructor:function(){var a=this.constructor.Interface,b;for(b in a)this[b]=
null;this.nativeEvent=this._targetInst=this.dispatchConfig=null;this.isPropagationStopped=this.isDefaultPrevented=D;this._dispatchInstances=this._dispatchListeners=null}});E.Interface={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(a){return a.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null};
E.extend=function(a){function b(){}function c(){return f.apply(this,arguments)}var f=this;b.prototype=f.prototype;var d=new b;k(d,c.prototype);c.prototype=d;c.prototype.constructor=c;c.Interface=k({},f.Interface,a);c.extend=f.extend;F(c);return c};F(E);function ba(a,b,c,f){if(this.eventPool.length){var d=this.eventPool.pop();this.call(d,a,b,c,f);return d}return new this(a,b,c,f)}
function ca(a){if(!(a instanceof this))throw Error(m(279));a.destructor();10>this.eventPool.length&&this.eventPool.push(a)}function F(a){a.eventPool=[];a.getPooled=ba;a.release=ca}var G=E.extend({touchHistory:function(){return null}});function H(a){return"touchstart"===a||"mousedown"===a}function I(a){return"touchmove"===a||"mousemove"===a}function J(a){return"touchend"===a||"touchcancel"===a||"mouseup"===a}
var K=["touchstart","mousedown"],L=["touchmove","mousemove"],N=["touchcancel","touchend","mouseup"],O=[],P={touchBank:O,numberActiveTouches:0,indexOfSingleActiveTouch:-1,mostRecentTimeStamp:0};function Q(a){return a.timeStamp||a.timestamp}function R(a){a=a.identifier;if(null==a)throw Error(m(138));return a}
function da(a){var b=R(a),c=O[b];c?(c.touchActive=!0,c.startPageX=a.pageX,c.startPageY=a.pageY,c.startTimeStamp=Q(a),c.currentPageX=a.pageX,c.currentPageY=a.pageY,c.currentTimeStamp=Q(a),c.previousPageX=a.pageX,c.previousPageY=a.pageY,c.previousTimeStamp=Q(a)):(c={touchActive:!0,startPageX:a.pageX,startPageY:a.pageY,startTimeStamp:Q(a),currentPageX:a.pageX,currentPageY:a.pageY,currentTimeStamp:Q(a),previousPageX:a.pageX,previousPageY:a.pageY,previousTimeStamp:Q(a)},O[b]=c);P.mostRecentTimeStamp=Q(a)}
function ea(a){var b=O[R(a)];b&&(b.touchActive=!0,b.previousPageX=b.currentPageX,b.previousPageY=b.currentPageY,b.previousTimeStamp=b.currentTimeStamp,b.currentPageX=a.pageX,b.currentPageY=a.pageY,b.currentTimeStamp=Q(a),P.mostRecentTimeStamp=Q(a))}function fa(a){var b=O[R(a)];b&&(b.touchActive=!1,b.previousPageX=b.currentPageX,b.previousPageY=b.currentPageY,b.previousTimeStamp=b.currentTimeStamp,b.currentPageX=a.pageX,b.currentPageY=a.pageY,b.currentTimeStamp=Q(a),P.mostRecentTimeStamp=Q(a))}
var S={recordTouchTrack:function(a,b){if(I(a))b.changedTouches.forEach(ea);else if(H(a))b.changedTouches.forEach(da),P.numberActiveTouches=b.touches.length,1===P.numberActiveTouches&&(P.indexOfSingleActiveTouch=b.touches[0].identifier);else if(J(a)&&(b.changedTouches.forEach(fa),P.numberActiveTouches=b.touches.length,1===P.numberActiveTouches))for(a=0;a<O.length;a++)if(b=O[a],null!=b&&b.touchActive){P.indexOfSingleActiveTouch=a;break}},touchHistory:P};
function T(a,b){if(null==b)throw Error(m(334));return null==a?b:Array.isArray(a)?a.concat(b):Array.isArray(b)?[a].concat(b):[a,b]}var U=null,V=0;function W(a,b){var c=U;U=a;if(null!==X.GlobalResponderHandler)X.GlobalResponderHandler.onChange(c,a,b)}
var Y={startShouldSetResponder:{phasedRegistrationNames:{bubbled:"onStartShouldSetResponder",captured:"onStartShouldSetResponderCapture"},dependencies:K},scrollShouldSetResponder:{phasedRegistrationNames:{bubbled:"onScrollShouldSetResponder",captured:"onScrollShouldSetResponderCapture"},dependencies:["scroll"]},selectionChangeShouldSetResponder:{phasedRegistrationNames:{bubbled:"onSelectionChangeShouldSetResponder",captured:"onSelectionChangeShouldSetResponderCapture"},dependencies:["selectionchange"]},
moveShouldSetResponder:{phasedRegistrationNames:{bubbled:"onMoveShouldSetResponder",captured:"onMoveShouldSetResponderCapture"},dependencies:L},responderStart:{registrationName:"onResponderStart",dependencies:K},responderMove:{registrationName:"onResponderMove",dependencies:L},responderEnd:{registrationName:"onResponderEnd",dependencies:N},responderRelease:{registrationName:"onResponderRelease",dependencies:N},responderTerminationRequest:{registrationName:"onResponderTerminationRequest",dependencies:[]},
responderGrant:{registrationName:"onResponderGrant",dependencies:[]},responderReject:{registrationName:"onResponderReject",dependencies:[]},responderTerminate:{registrationName:"onResponderTerminate",dependencies:[]}},X={_getResponder:function(){return U},eventTypes:Y,extractEvents:function(a,b,c,f){if(H(a))V+=1;else if(J(a))if(0<=V)--V;else return null;S.recordTouchTrack(a,c);if(b&&("scroll"===a&&!c.responderIgnoreScroll||0<V&&"selectionchange"===a||H(a)||I(a))){var d=H(a)?Y.startShouldSetResponder:
I(a)?Y.moveShouldSetResponder:"selectionchange"===a?Y.selectionChangeShouldSetResponder:Y.scrollShouldSetResponder;if(U)b:{var e=U;for(var g=0,p=e;p;p=u(p))g++;p=0;for(var M=b;M;M=u(M))p++;for(;0<g-p;)e=u(e),g--;for(;0<p-g;)b=u(b),p--;for(;g--;){if(e===b||e===b.alternate)break b;e=u(e);b=u(b)}e=null}else e=b;b=e===U;e=G.getPooled(d,e,c,f);e.touchHistory=S.touchHistory;b?y(e,aa):y(e,A);b:{d=e._dispatchListeners;b=e._dispatchInstances;if(Array.isArray(d))for(g=0;g<d.length&&!e.isPropagationStopped();g++){if(d[g](e,
b[g])){d=b[g];break b}}else if(d&&d(e,b)){d=b;break b}d=null}e._dispatchInstances=null;e._dispatchListeners=null;e.isPersistent()||e.constructor.release(e);if(d&&d!==U)if(e=G.getPooled(Y.responderGrant,d,c,f),e.touchHistory=S.touchHistory,y(e,B),b=!0===t(e),U)if(g=G.getPooled(Y.responderTerminationRequest,U,c,f),g.touchHistory=S.touchHistory,y(g,B),p=!g._dispatchListeners||t(g),g.isPersistent()||g.constructor.release(g),p){g=G.getPooled(Y.responderTerminate,U,c,f);g.touchHistory=S.touchHistory;y(g,
B);var l=T(l,[e,g]);W(d,b)}else d=G.getPooled(Y.responderReject,d,c,f),d.touchHistory=S.touchHistory,y(d,B),l=T(l,d);else l=T(l,e),W(d,b);else l=null}else l=null;d=U&&H(a);e=U&&I(a);b=U&&J(a);if(d=d?Y.responderStart:e?Y.responderMove:b?Y.responderEnd:null)d=G.getPooled(d,U,c,f),d.touchHistory=S.touchHistory,y(d,B),l=T(l,d);d=U&&"touchcancel"===a;if(a=U&&!d&&J(a))a:{if((a=c.touches)&&0!==a.length)for(e=0;e<a.length;e++)if(b=a[e].target,null!==b&&void 0!==b&&0!==b){g=q(b);b:{for(b=U;g;){if(b===g||b===
g.alternate){b=!0;break b}g=u(g)}b=!1}if(b){a=!1;break a}}a=!0}if(a=d?Y.responderTerminate:a?Y.responderRelease:null)c=G.getPooled(a,U,c,f),c.touchHistory=S.touchHistory,y(c,B),l=T(l,c),W(null);return l},GlobalResponderHandler:null,injection:{injectGlobalResponderHandler:function(a){X.GlobalResponderHandler=a}}},Z=h.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Events,ha=Z[3],ia=Z[0],ja=Z[1];n=Z[2];q=ia;r=ja;module.exports={__proto__:null,ResponderEventPlugin:X,ResponderTouchHistoryStore:S,injectEventPluginsByName:ha};
