import { createClient } from '@supabase/supabase-js';
import config, { validateConfiguration } from '../config/environment';

// Validate configuration before initializing Supabase
validateConfiguration();

// Get Supabase configuration from environment
const { url: supabaseUrl, anon<PERSON>ey: supabaseAnonKey } = config.supabase;

// Initialize the Supabase client with production-ready configuration
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    // Enable phone authentication
    flowType: 'pkce',
  },
  // Global configuration
  global: {
    headers: {
      'X-Client-Info': 'jiranipay-mobile-app',
    },
  },
});

export default supabase;