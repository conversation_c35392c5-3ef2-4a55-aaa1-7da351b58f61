import { createClient } from '@supabase/supabase-js';

// ✅ DIRECT SUPABASE CONFIGURATION - Your Real Credentials
const supabaseUrl = 'https://rridzjvfjvzaumepzmss.supabase.co';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJyaWR6anZmanZ6YXVtZXB6bXNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MTQwOTksImV4cCI6MjA2NTA5MDA5OX0.X5LOTKHzOUXAJcabvPa1WLfl4lj2HZ5aHPG3QoeafFI';

// Debug logging
console.log('🔧 Supabase Configuration:');
console.log('- URL:', supabaseUrl);
console.log('- Key (first 20 chars):', supabaseAnonKey.substring(0, 20) + '...');

// Validate credentials format
if (!supabaseUrl.startsWith('https://') || !supabaseAnonKey.startsWith('eyJ')) {
  console.error('🚨 Invalid Supabase credentials format!');
  throw new Error('Invalid Supabase configuration');
}

// Initialize the Supabase client with production-ready configuration
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    // Enable phone authentication
    flowType: 'pkce',
  },
  // Global configuration
  global: {
    headers: {
      'X-Client-Info': 'jiranipay-mobile-app',
    },
  },
});

console.log('✅ Supabase client initialized successfully');

export default supabase;