import { createClient } from '@supabase/supabase-js';

// ⚠️  IMPORTANT: Replace these with your actual Supabase project credentials
// Get these from: https://app.supabase.com/project/YOUR_PROJECT/settings/api

// TODO: Replace with your actual Supabase URL from your project dashboard
const supabaseUrl = 'YOUR_SUPABASE_PROJECT_URL'; // e.g., 'https://abcdefghijklmnop.supabase.co'

// TODO: Replace with your actual anon/public key from your project dashboard
const supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY'; // Starts with 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...'

// Validate that credentials are properly configured
if (supabaseUrl === 'YOUR_SUPABASE_PROJECT_URL' || supabaseAnonKey === 'YOUR_SUPABASE_ANON_KEY') {
  console.error('🚨 SUPABASE CONFIGURATION ERROR:');
  console.error('Please update supabaseClient.js with your actual Supabase credentials');
  console.error('1. Go to https://app.supabase.com/project/YOUR_PROJECT/settings/api');
  console.error('2. Copy your Project URL and anon public key');
  console.error('3. Replace the placeholder values in services/supabaseClient.js');
  throw new Error('Supabase credentials not configured. Please check the console for setup instructions.');
}

// Initialize the Supabase client with production-ready configuration
const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
    // Enable phone authentication
    flowType: 'pkce',
  },
  // Global configuration
  global: {
    headers: {
      'X-Client-Info': 'jiranipay-mobile-app',
    },
  },
});

export default supabase;