import React, { createContext, useContext, useState, useEffect } from 'react';
import { 
  initializeI18n, 
  changeLanguage, 
  getCurrentLanguage, 
  getCurrentLanguageConfig,
  getTimeBasedGreeting,
  isRTL,
  t 
} from '../utils/i18n';

/**
 * Language Context for managing app-wide language state
 * Provides translation functions and language management
 */
const LanguageContext = createContext({
  currentLanguage: 'en',
  currentLanguageConfig: null,
  isRTL: false,
  t: () => '',
  getTimeBasedGreeting: () => '',
  changeLanguage: () => {},
  initializeLanguage: () => {},
});

/**
 * Language Provider Component
 * Wraps the app to provide language context to all components
 */
export const LanguageProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState('en');
  const [currentLanguageConfig, setCurrentLanguageConfig] = useState(null);
  const [isRTLMode, setIsRTLMode] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);

  /**
   * Initialize language system
   * @param {string} selectedCountryCode - Country code to determine default language
   */
  const initializeLanguage = async (selectedCountryCode = 'UG') => {
    try {
      const language = await initializeI18n(selectedCountryCode);
      setCurrentLanguage(language);
      setCurrentLanguageConfig(getCurrentLanguageConfig());
      setIsRTLMode(isRTL());
      setIsInitialized(true);
    } catch (error) {
      console.error('Error initializing language:', error);
      // Fallback to English
      setCurrentLanguage('en');
      setIsRTLMode(false);
      setIsInitialized(true);
    }
  };

  /**
   * Change app language
   * @param {string} languageCode - New language code
   */
  const handleLanguageChange = async (languageCode) => {
    try {
      await changeLanguage(languageCode);
      setCurrentLanguage(languageCode);
      setCurrentLanguageConfig(getCurrentLanguageConfig());
      setIsRTLMode(isRTL());
    } catch (error) {
      console.error('Error changing language:', error);
    }
  };

  /**
   * Get localized time-based greeting
   * @param {string} name - Optional user name
   * @returns {string} - Localized greeting
   */
  const getLocalizedGreeting = (name = null) => {
    return getTimeBasedGreeting(name);
  };

  // Initialize language on mount
  useEffect(() => {
    initializeLanguage();
  }, []);

  const contextValue = {
    currentLanguage,
    currentLanguageConfig,
    isRTL: isRTLMode,
    isInitialized,
    t,
    getTimeBasedGreeting: getLocalizedGreeting,
    changeLanguage: handleLanguageChange,
    initializeLanguage,
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

/**
 * Hook to use language context
 * @returns {Object} - Language context value
 */
export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

/**
 * Higher-order component to inject language props
 * @param {Component} WrappedComponent - Component to wrap
 * @returns {Component} - Enhanced component with language props
 */
export const withLanguage = (WrappedComponent) => {
  return function LanguageEnhancedComponent(props) {
    const languageContext = useLanguage();
    return <WrappedComponent {...props} {...languageContext} />;
  };
};

export default LanguageContext;
