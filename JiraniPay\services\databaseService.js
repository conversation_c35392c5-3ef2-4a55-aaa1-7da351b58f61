/**
 * Database Service for JiraniPay
 * 
 * This service handles all database operations using Supabase.
 * It provides a clean interface for user management, profiles, and transactions.
 */

import supabase from './supabaseClient';
import config from '../config/environment';

class DatabaseService {
  constructor() {
    this.tableName = {
      userProfiles: 'user_profiles',
      userPreferences: 'user_preferences',
      paymentAccounts: 'payment_accounts',
      transactions: 'transactions',
    };
  }

  /**
   * Create user profile after successful authentication
   * @param {Object} profileData - User profile information
   * @returns {Promise<Object>} - Result object with success status
   */
  async createUserProfile(profileData) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.userProfiles)
        .insert([
          {
            id: profileData.userId,
            full_name: profileData.fullName,
            phone_number: profileData.phoneNumber,
            country_code: profileData.countryCode || config.regional.defaultCountry,
            preferred_language: profileData.preferredLanguage || config.regional.defaultLanguage,
          }
        ])
        .select()
        .single();

      if (error) throw error;

      // Also create user preferences with defaults
      await this.createUserPreferences(profileData.userId);

      console.log('✅ User profile created successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error creating user profile:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create user preferences with default values
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Result object with success status
   */
  async createUserPreferences(userId) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.userPreferences)
        .insert([
          {
            id: userId,
            biometric_enabled: false,
            notifications_enabled: true,
            dark_mode_enabled: false,
            preferred_currency: config.regional.defaultCurrency,
          }
        ])
        .select()
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error creating user preferences:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user profile by user ID
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Result object with user profile data
   */
  async getUserProfile(userId) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.userProfiles)
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error getting user profile:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update user profile
   * @param {string} userId - User ID
   * @param {Object} updates - Profile updates
   * @returns {Promise<Object>} - Result object with success status
   */
  async updateUserProfile(userId, updates) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.userProfiles)
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ User profile updated successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error updating user profile:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user preferences
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Result object with user preferences
   */
  async getUserPreferences(userId) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.userPreferences)
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error getting user preferences:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update user preferences
   * @param {string} userId - User ID
   * @param {Object} preferences - Preference updates
   * @returns {Promise<Object>} - Result object with success status
   */
  async updateUserPreferences(userId, preferences) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.userPreferences)
        .update({
          ...preferences,
          updated_at: new Date().toISOString(),
        })
        .eq('id', userId)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ User preferences updated successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error updating user preferences:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Add payment account for user
   * @param {string} userId - User ID
   * @param {Object} accountData - Payment account information
   * @returns {Promise<Object>} - Result object with success status
   */
  async addPaymentAccount(userId, accountData) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.paymentAccounts)
        .insert([
          {
            user_id: userId,
            account_type: accountData.accountType,
            provider_name: accountData.providerName,
            account_number: accountData.accountNumber,
            account_name: accountData.accountName,
            is_primary: accountData.isPrimary || false,
          }
        ])
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Payment account added successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error adding payment account:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user's payment accounts
   * @param {string} userId - User ID
   * @returns {Promise<Object>} - Result object with payment accounts
   */
  async getUserPaymentAccounts(userId) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.paymentAccounts)
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('is_primary', { ascending: false })
        .order('created_at', { ascending: false });

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error getting payment accounts:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Create transaction record
   * @param {Object} transactionData - Transaction information
   * @returns {Promise<Object>} - Result object with success status
   */
  async createTransaction(transactionData) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.transactions)
        .insert([
          {
            user_id: transactionData.userId,
            transaction_type: transactionData.transactionType,
            amount: transactionData.amount,
            currency: transactionData.currency || config.regional.defaultCurrency,
            status: transactionData.status || 'pending',
            reference_number: transactionData.referenceNumber,
            description: transactionData.description,
            metadata: transactionData.metadata || {},
          }
        ])
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Transaction created successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error creating transaction:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Get user's transaction history
   * @param {string} userId - User ID
   * @param {Object} options - Query options (limit, offset, status filter)
   * @returns {Promise<Object>} - Result object with transactions
   */
  async getUserTransactions(userId, options = {}) {
    try {
      let query = supabase
        .from(this.tableName.transactions)
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      // Apply filters
      if (options.status) {
        query = query.eq('status', options.status);
      }

      if (options.transactionType) {
        query = query.eq('transaction_type', options.transactionType);
      }

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) throw error;

      return { success: true, data };
    } catch (error) {
      console.error('❌ Error getting user transactions:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Update transaction status
   * @param {string} transactionId - Transaction ID
   * @param {string} status - New status
   * @param {Object} metadata - Additional metadata
   * @returns {Promise<Object>} - Result object with success status
   */
  async updateTransactionStatus(transactionId, status, metadata = {}) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.transactions)
        .update({
          status,
          metadata,
          updated_at: new Date().toISOString(),
        })
        .eq('id', transactionId)
        .select()
        .single();

      if (error) throw error;

      console.log('✅ Transaction status updated successfully');
      return { success: true, data };
    } catch (error) {
      console.error('❌ Error updating transaction status:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Check if user profile exists
   * @param {string} userId - User ID
   * @returns {Promise<boolean>} - Whether profile exists
   */
  async userProfileExists(userId) {
    try {
      const { data, error } = await supabase
        .from(this.tableName.userProfiles)
        .select('id')
        .eq('id', userId)
        .single();

      return !error && data;
    } catch (error) {
      return false;
    }
  }
}

// Create and export a singleton instance
const databaseService = new DatabaseService();
export default databaseService;
