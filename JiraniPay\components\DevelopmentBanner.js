import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { Colors } from '../constants/Colors';

/**
 * Development Banner Component
 * Shows a banner when the app is running in development mode
 */
const DevelopmentBanner = ({ visible = true }) => {
  if (!visible) return null;

  return (
    <View style={styles.banner}>
      <Text style={styles.bannerText}>
        🚧 DEVELOPMENT MODE 🚧
      </Text>
      <Text style={styles.bannerSubtext}>
        OTP: Use any 6-digit code (e.g., 123456)
      </Text>
    </View>
  );
};

const styles = StyleSheet.create({
  banner: {
    backgroundColor: Colors.accent.coral,
    paddingVertical: 8,
    paddingHorizontal: 16,
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: Colors.accent.coralDark,
  },
  bannerText: {
    color: Colors.neutral.white,
    fontSize: 12,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  bannerSubtext: {
    color: Colors.neutral.white,
    fontSize: 10,
    textAlign: 'center',
    marginTop: 2,
    opacity: 0.9,
  },
});

export default DevelopmentBanner;
