{"version": 3, "names": ["_AsyncStorage", "_interopRequireDefault", "require", "e", "__esModule", "default", "useAsyncStorage", "key", "getItem", "args", "AsyncStorage", "setItem", "mergeItem", "removeItem"], "sourceRoot": "../../src", "sources": ["hooks.ts"], "mappings": ";;;;;;AAAA,IAAAA,aAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0C,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAGnC,SAASG,eAAeA,CAACC,GAAW,EAAoB;EAC7D,OAAO;IACLC,OAAO,EAAEA,CAAC,GAAGC,IAAI,KAAKC,qBAAY,CAACF,OAAO,CAACD,GAAG,EAAE,GAAGE,IAAI,CAAC;IACxDE,OAAO,EAAEA,CAAC,GAAGF,IAAI,KAAKC,qBAAY,CAACC,OAAO,CAACJ,GAAG,EAAE,GAAGE,IAAI,CAAC;IACxDG,SAAS,EAAEA,CAAC,GAAGH,IAAI,KAAKC,qBAAY,CAACE,SAAS,CAACL,GAAG,EAAE,GAAGE,IAAI,CAAC;IAC5DI,UAAU,EAAEA,CAAC,GAAGJ,IAAI,KAAKC,qBAAY,CAACG,UAAU,CAACN,GAAG,EAAE,GAAGE,IAAI;EAC/D,CAAC;AACH", "ignoreList": []}