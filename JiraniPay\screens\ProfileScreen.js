import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { Colors } from '../constants/Colors';
import authService from '../services/authService';
import databaseService from '../services/databaseService';

const ProfileScreen = ({ navigation }) => {
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [userPreferences, setUserPreferences] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      setLoading(true);

      // Get current user
      const currentUser = authService.getCurrentUser();
      if (currentUser) {
        setUser(currentUser);

        // Try to load user profile (may fail due to UUID issue in development)
        try {
          const profileResult = await authService.getUserProfile(currentUser.id);
          if (profileResult.success) {
            setUserProfile(profileResult.data);
          }
        } catch (profileError) {
          console.log('⚠️ Profile loading failed (expected in development)');
        }

        // Try to load user preferences (may fail due to UUID issue in development)
        try {
          const preferencesResult = await databaseService.getUserPreferences(currentUser.id);
          if (preferencesResult.success) {
            setUserPreferences(preferencesResult.data);
          } else {
            // Set default preferences for development
            setUserPreferences({
              biometric_enabled: false,
              dark_mode_enabled: false,
              notifications_enabled: true,
              preferred_language: 'en',
              preferred_currency: 'UGX',
            });
          }
        } catch (preferencesError) {
          console.log('⚠️ Preferences loading failed (expected in development)');
          // Set default preferences for development
          setUserPreferences({
            biometric_enabled: false,
            dark_mode_enabled: false,
            notifications_enabled: true,
            preferred_language: 'en',
            preferred_currency: 'UGX',
          });
        }
      } else {
        console.warn('⚠️ No current user found');
      }
    } catch (error) {
      console.error('❌ Error loading user data:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadUserData();
    setRefreshing(false);
  };

  const handleImagePicker = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert(
      'Coming Soon',
      'Profile picture upload will be available soon!',
      [{ text: 'OK' }]
    );
  };

  const togglePreference = async (key, value) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

      // Update local state immediately for better UX
      setUserPreferences(prev => ({ ...prev, [key]: value }));

      // Try to update in database (may fail in development due to UUID issue)
      if (user && user.id) {
        try {
          await databaseService.updateUserPreferences(user.id, { [key]: value });
        } catch (error) {
          console.log('⚠️ Database update failed (expected in development)');
        }
      }
    } catch (error) {
      console.error('Error updating preference:', error);
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await authService.signOut();
              // Navigation will be handled by auth state change
            } catch (error) {
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          },
        },
      ]
    );
  };

  const renderProfileHeader = () => (
    <LinearGradient
      colors={Colors.gradients.sunset}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.profileHeader}
    >
      <View style={styles.profileImageContainer}>
        <TouchableOpacity onPress={handleImagePicker} style={styles.imageWrapper}>
          {userProfile?.profile_picture_url ? (
            <Image
              source={{ uri: userProfile.profile_picture_url }}
              style={styles.profileImage}
            />
          ) : (
            <View style={styles.defaultAvatar}>
              <Ionicons name="person" size={40} color={Colors.neutral.warmGray} />
            </View>
          )}
          <View style={styles.editImageButton}>
            <Ionicons name="camera" size={16} color={Colors.neutral.white} />
          </View>
        </TouchableOpacity>
      </View>
      
      <View style={styles.profileInfo}>
        <Text style={styles.profileName}>
          {userProfile?.full_name || user?.user_metadata?.full_name || 'User'}
        </Text>
        <Text style={styles.profilePhone}>
          {userProfile?.phone_number || user?.phone || 'Phone not available'}
        </Text>
        <View style={styles.verificationBadge}>
          <Ionicons 
            name={userProfile?.is_verified ? "checkmark-circle" : "time-outline"} 
            size={16} 
            color={Colors.neutral.white} 
          />
          <Text style={styles.verificationText}>
            {userProfile?.is_verified ? 'Verified Account' : 'Pending Verification'}
          </Text>
        </View>
      </View>
    </LinearGradient>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary.main} />
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {renderProfileHeader()}
      
      {/* Quick Stats */}
      <View style={styles.quickStats}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>UGX 0</Text>
          <Text style={styles.statLabel}>Wallet Balance</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>0</Text>
          <Text style={styles.statLabel}>Transactions</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {userProfile?.verification_level || 'Basic'}
          </Text>
          <Text style={styles.statLabel}>Account Level</Text>
        </View>
      </View>

      {/* Account Management */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Account Management</Text>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Coming Soon', 'Edit profile feature will be available soon.')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.primary.main }]}>
              <Ionicons name="person-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Edit Profile</Text>
              <Text style={styles.menuItemSubtitle}>Update your personal information</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Coming Soon', 'Verification feature will be available soon.')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.savanna }]}>
              <Ionicons name="shield-checkmark-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Account Verification</Text>
              <Text style={styles.menuItemSubtitle}>Verify your identity for higher limits</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>
      </View>

      {/* Security & Privacy */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Security & Privacy</Text>

        <View style={styles.menuItem}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.earth }]}>
              <Ionicons name="finger-print-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Biometric Authentication</Text>
              <Text style={styles.menuItemSubtitle}>Use fingerprint or face ID</Text>
            </View>
          </View>
          <Switch
            value={userPreferences?.biometric_enabled || false}
            onValueChange={(value) => togglePreference('biometric_enabled', value)}
            trackColor={{ false: Colors.neutral.warmGrayLight, true: Colors.primary.light }}
            thumbColor={userPreferences?.biometric_enabled ? Colors.primary.main : Colors.neutral.white}
          />
        </View>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Coming Soon', 'Two-Factor Authentication will be available soon.')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.accent.coral }]}>
              <Ionicons name="lock-closed-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Two-Factor Authentication</Text>
              <Text style={styles.menuItemSubtitle}>Add extra security to your account</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Coming Soon', 'Security settings will be available soon.')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.lake }]}>
              <Ionicons name="key-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Change PIN</Text>
              <Text style={styles.menuItemSubtitle}>Update your transaction PIN</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>
      </View>

      {/* App Preferences */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>App Preferences</Text>

        <View style={styles.menuItem}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.neutral.charcoal }]}>
              <Ionicons name="moon-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Dark Mode</Text>
              <Text style={styles.menuItemSubtitle}>Switch to dark theme</Text>
            </View>
          </View>
          <Switch
            value={userPreferences?.dark_mode_enabled || false}
            onValueChange={(value) => togglePreference('dark_mode_enabled', value)}
            trackColor={{ false: Colors.neutral.warmGrayLight, true: Colors.primary.light }}
            thumbColor={userPreferences?.dark_mode_enabled ? Colors.primary.main : Colors.neutral.white}
          />
        </View>

        <View style={styles.menuItem}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.accent.gold }]}>
              <Ionicons name="notifications-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Notifications</Text>
              <Text style={styles.menuItemSubtitle}>Manage notification preferences</Text>
            </View>
          </View>
          <Switch
            value={userPreferences?.notifications_enabled || false}
            onValueChange={(value) => togglePreference('notifications_enabled', value)}
            trackColor={{ false: Colors.neutral.warmGrayLight, true: Colors.primary.light }}
            thumbColor={userPreferences?.notifications_enabled ? Colors.primary.main : Colors.neutral.white}
          />
        </View>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Coming Soon', 'Language selection will be available soon.')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.primary.light }]}>
              <Ionicons name="language-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Language</Text>
              <Text style={styles.menuItemSubtitle}>English</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Coming Soon', 'Currency selection will be available soon.')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.earth }]}>
              <Ionicons name="card-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Currency</Text>
              <Text style={styles.menuItemSubtitle}>Ugandan Shilling</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>
      </View>

      {/* Help & Support */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Help & Support</Text>

        <TouchableOpacity style={styles.menuItem} onPress={() => {
          try {
            navigation.navigate('FAQ');
          } catch (error) {
            Alert.alert('FAQ', 'Comprehensive FAQ section with guides for:\n\n• How to send money\n• Bill payment process\n• Wallet top-up methods\n• QR code usage\n• Account verification\n• Security best practices\n\nFull FAQ section coming soon!');
          }
        }}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.lake }]}>
              <Ionicons name="help-circle-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Frequently Asked Questions</Text>
              <Text style={styles.menuItemSubtitle}>Get answers to common questions</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => {
          try {
            navigation.navigate('AIAssistant');
          } catch (error) {
            Alert.alert('AI Assistant', 'Your personal AI assistant will help with:\n\n• App navigation guidance\n• Transaction assistance\n• Account troubleshooting\n• Financial advice & insights\n• Real-time support\n\nAI Assistant coming soon with advanced features!');
          }
        }}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.accent.gold }]}>
              <Ionicons name="chatbubble-ellipses-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>AI Assistant</Text>
              <Text style={styles.menuItemSubtitle}>Get instant help and financial advice</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Contact Support', 'Email: <EMAIL>\nPhone: +256 700 000 000\n\nOur customer service team is available 24/7 to assist you with any questions or issues.')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.primary.main }]}>
              <Ionicons name="call-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Contact Support</Text>
              <Text style={styles.menuItemSubtitle}>Reach our customer service team</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>
      </View>

      {/* Logout */}
      <View style={styles.section}>
        <TouchableOpacity style={[styles.menuItem, styles.logoutButton]} onPress={handleLogout}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.status.error }]}>
              <Ionicons name="log-out-outline" size={20} color={Colors.neutral.white} />
            </View>
            <Text style={[styles.menuItemTitle, { color: Colors.status.error }]}>Logout</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Bottom Spacing */}
      <View style={styles.bottomSpacing} />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.neutral.cream,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.neutral.warmGray,
  },
  profileHeader: {
    paddingTop: 60,
    paddingBottom: 32,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  profileImageContainer: {
    marginBottom: 16,
  },
  imageWrapper: {
    position: 'relative',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: Colors.neutral.white,
  },
  defaultAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.neutral.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: Colors.neutral.white,
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary.dark,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.neutral.white,
  },
  profileInfo: {
    alignItems: 'center',
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    marginBottom: 4,
  },
  profilePhone: {
    fontSize: 16,
    color: Colors.neutral.white,
    opacity: 0.9,
    marginBottom: 8,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  verificationText: {
    color: Colors.neutral.white,
    fontSize: 14,
    marginLeft: 4,
    fontWeight: '500',
  },
  quickStats: {
    flexDirection: 'row',
    backgroundColor: Colors.neutral.white,
    marginHorizontal: 24,
    marginTop: -16,
    borderRadius: 16,
    paddingVertical: 20,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    backgroundColor: Colors.neutral.creamDark,
    marginVertical: 8,
  },
  section: {
    marginTop: 24,
    marginHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  menuItemSubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  logoutButton: {
    borderColor: Colors.status.error,
    borderWidth: 1,
    backgroundColor: 'rgba(192, 57, 43, 0.05)',
  },
  bottomSpacing: {
    height: 100,
  },
});

export default ProfileScreen;
