import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Switch,
  Image,
  Dimensions,
  ActivityIndicator,
  RefreshControl,
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
// TODO: Install expo-image-picker package for profile picture functionality
import { Colors } from '../constants/Colors';
import authService from '../services/authService';
import databaseService from '../services/databaseService';

const { width, height } = Dimensions.get('window');

const ProfileScreen = ({ navigation }) => {
  const [user, setUser] = useState(null);
  const [userProfile, setUserProfile] = useState(null);
  const [userPreferences, setUserPreferences] = useState(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [profileImage, setProfileImage] = useState(null);

  useEffect(() => {
    loadUserData();
  }, []);

  const loadUserData = async () => {
    try {
      setLoading(true);
      
      // Get current user
      const currentUser = authService.getCurrentUser();
      if (currentUser) {
        setUser(currentUser);
        
        // Load user profile
        const profileResult = await authService.getUserProfile(currentUser.id);
        if (profileResult.success) {
          setUserProfile(profileResult.data);
        }
        
        // Load user preferences
        const preferencesResult = await databaseService.getUserPreferences(currentUser.id);
        if (preferencesResult.success) {
          setUserPreferences(preferencesResult.data);
        } else {
          // Create default preferences if they don't exist
          const createResult = await databaseService.createUserPreferences(currentUser.id);
          if (createResult.success) {
            setUserPreferences(createResult.data);
          }
        }
      }
    } catch (error) {
      console.error('❌ Error loading user data:', error);
      Alert.alert('Error', 'Failed to load profile data. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadUserData();
    setRefreshing(false);
  };

  const handleImagePicker = async () => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      // TODO: Implement image picker when expo-image-picker is installed
      Alert.alert(
        'Coming Soon',
        'Profile picture upload will be available soon!',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error with image picker:', error);
      Alert.alert('Error', 'Failed to update profile picture.');
    }
  };

  const togglePreference = async (key, value) => {
    try {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      
      const updates = { [key]: value };
      const result = await databaseService.updateUserPreferences(user.id, updates);
      
      if (result.success) {
        setUserPreferences(prev => ({ ...prev, [key]: value }));
      } else {
        Alert.alert('Error', 'Failed to update preference. Please try again.');
      }
    } catch (error) {
      console.error('Error updating preference:', error);
      Alert.alert('Error', 'Failed to update preference.');
    }
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            try {
              await authService.signOut();
              // Navigation will be handled by auth state change
            } catch (error) {
              Alert.alert('Error', 'Failed to logout. Please try again.');
            }
          },
        },
      ]
    );
  };

  const renderProfileHeader = () => (
    <LinearGradient
      colors={Colors.gradients.sunset}
      start={{ x: 0, y: 0 }}
      end={{ x: 1, y: 1 }}
      style={styles.profileHeader}
    >
      <View style={styles.profileImageContainer}>
        <TouchableOpacity onPress={handleImagePicker} style={styles.imageWrapper}>
          {profileImage || userProfile?.profile_picture_url ? (
            <Image
              source={{ uri: profileImage || userProfile.profile_picture_url }}
              style={styles.profileImage}
            />
          ) : (
            <View style={styles.defaultAvatar}>
              <Ionicons name="person" size={40} color={Colors.neutral.warmGray} />
            </View>
          )}
          <View style={styles.editImageButton}>
            <Ionicons name="camera" size={16} color={Colors.neutral.white} />
          </View>
        </TouchableOpacity>
      </View>
      
      <View style={styles.profileInfo}>
        <Text style={styles.profileName}>
          {userProfile?.full_name || user?.user_metadata?.full_name || 'User'}
        </Text>
        <Text style={styles.profilePhone}>
          {userProfile?.phone_number || user?.phone || 'Phone not available'}
        </Text>
        <View style={styles.verificationBadge}>
          <Ionicons 
            name={userProfile?.is_verified ? "checkmark-circle" : "time-outline"} 
            size={16} 
            color={Colors.neutral.white} 
          />
          <Text style={styles.verificationText}>
            {userProfile?.is_verified ? 'Verified Account' : 'Pending Verification'}
          </Text>
        </View>
      </View>
    </LinearGradient>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary.main} />
        <Text style={styles.loadingText}>Loading profile...</Text>
      </View>
    );
  }

  return (
    <ScrollView
      style={styles.container}
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {renderProfileHeader()}
      
      {/* Quick Stats */}
      <View style={styles.quickStats}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>UGX 0</Text>
          <Text style={styles.statLabel}>Wallet Balance</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>0</Text>
          <Text style={styles.statLabel}>Transactions</Text>
        </View>
        <View style={styles.statDivider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {userProfile?.verification_level || 'Basic'}
          </Text>
          <Text style={styles.statLabel}>Account Level</Text>
        </View>
      </View>

      {/* Account Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Account Settings</Text>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Coming Soon', 'Edit profile feature will be available soon.')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.primary.light }]}>
              <Ionicons name="person-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Edit Profile</Text>
              <Text style={styles.menuItemSubtitle}>Update your personal information</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Coming Soon', 'Verification feature will be available soon.')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.savanna }]}>
              <Ionicons name="shield-checkmark-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Account Verification</Text>
              <Text style={styles.menuItemSubtitle}>Verify your identity for higher limits</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>
      </View>

      {/* Preferences */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Preferences</Text>

        <View style={styles.menuItem}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.accent.coral }]}>
              <Ionicons name="finger-print-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Biometric Authentication</Text>
              <Text style={styles.menuItemSubtitle}>Use fingerprint or face ID</Text>
            </View>
          </View>
          <Switch
            value={userPreferences?.biometric_enabled || false}
            onValueChange={(value) => togglePreference('biometric_enabled', value)}
            trackColor={{ false: Colors.neutral.warmGrayLight, true: Colors.primary.light }}
            thumbColor={userPreferences?.biometric_enabled ? Colors.primary.main : Colors.neutral.white}
          />
        </View>

        <View style={styles.menuItem}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.neutral.charcoal }]}>
              <Ionicons name="moon-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Dark Mode</Text>
              <Text style={styles.menuItemSubtitle}>Switch to dark theme</Text>
            </View>
          </View>
          <Switch
            value={userPreferences?.dark_mode_enabled || false}
            onValueChange={(value) => togglePreference('dark_mode_enabled', value)}
            trackColor={{ false: Colors.neutral.warmGrayLight, true: Colors.primary.light }}
            thumbColor={userPreferences?.dark_mode_enabled ? Colors.primary.main : Colors.neutral.white}
          />
        </View>

        <View style={styles.menuItem}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.lake }]}>
              <Ionicons name="notifications-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Notifications</Text>
              <Text style={styles.menuItemSubtitle}>Manage notification preferences</Text>
            </View>
          </View>
          <Switch
            value={userPreferences?.notifications_enabled || false}
            onValueChange={(value) => togglePreference('notifications_enabled', value)}
            trackColor={{ false: Colors.neutral.warmGrayLight, true: Colors.primary.light }}
            thumbColor={userPreferences?.notifications_enabled ? Colors.primary.main : Colors.neutral.white}
          />
        </View>

        <TouchableOpacity style={styles.menuItem} onPress={() => setLanguageModal(true)}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.accent.gold }]}>
              <Ionicons name="language-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Language</Text>
              <Text style={styles.menuItemSubtitle}>
                {languages.find(lang => lang.code === (userPreferences?.preferred_language || 'en'))?.name || 'English'}
              </Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => setCurrencyModal(true)}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.earth }]}>
              <Ionicons name="card-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Currency</Text>
              <Text style={styles.menuItemSubtitle}>
                {currencies.find(curr => curr.code === (userPreferences?.preferred_currency || 'UGX'))?.name || 'Ugandan Shilling'}
              </Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>
      </View>

      {/* Security */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Security</Text>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Coming Soon', 'Change PIN feature will be available soon.')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.heritage }]}>
              <Ionicons name="keypad-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Change PIN</Text>
              <Text style={styles.menuItemSubtitle}>Update your security PIN</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Coming Soon', 'Transaction limits feature will be available soon.')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.earth }]}>
              <Ionicons name="card-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Transaction Limits</Text>
              <Text style={styles.menuItemSubtitle}>Manage daily and monthly limits</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>
      </View>

      {/* Support & Help */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Support & Help</Text>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Contact Support', 'Email: <EMAIL>\nPhone: +256 700 000 000')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.secondary.savanna }]}>
              <Ionicons name="help-circle-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Help & Support</Text>
              <Text style={styles.menuItemSubtitle}>Get help with your account</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={() => Alert.alert('Coming Soon', 'Terms and Privacy will be available soon.')}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.neutral.warmGray }]}>
              <Ionicons name="document-text-outline" size={20} color={Colors.neutral.white} />
            </View>
            <View>
              <Text style={styles.menuItemTitle}>Terms & Privacy</Text>
              <Text style={styles.menuItemSubtitle}>Read our terms and privacy policy</Text>
            </View>
          </View>
          <Ionicons name="chevron-forward" size={20} color={Colors.neutral.warmGray} />
        </TouchableOpacity>
      </View>

      {/* Logout */}
      <View style={styles.section}>
        <TouchableOpacity style={[styles.menuItem, styles.logoutButton]} onPress={handleLogout}>
          <View style={styles.menuItemLeft}>
            <View style={[styles.menuIcon, { backgroundColor: Colors.status.error }]}>
              <Ionicons name="log-out-outline" size={20} color={Colors.neutral.white} />
            </View>
            <Text style={[styles.menuItemTitle, { color: Colors.status.error }]}>Logout</Text>
          </View>
        </TouchableOpacity>
      </View>

      {/* Bottom Spacing */}
      <View style={styles.bottomSpacing} />

      {/* Edit Profile Modal */}
      <Modal
        visible={editProfileModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setEditProfileModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setEditProfileModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Edit Profile</Text>
            <TouchableOpacity onPress={() => {
              // TODO: Save profile changes
              setEditProfileModal(false);
              Alert.alert('Success', 'Profile updated successfully!');
            }}>
              <Text style={styles.modalSaveText}>Save</Text>
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Full Name</Text>
              <TextInput
                style={styles.textInput}
                value={editForm.fullName}
                onChangeText={(text) => setEditForm(prev => ({ ...prev, fullName: text }))}
                placeholder="Enter your full name"
                placeholderTextColor={Colors.neutral.warmGray}
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Email Address</Text>
              <TextInput
                style={styles.textInput}
                value={editForm.email}
                onChangeText={(text) => setEditForm(prev => ({ ...prev, email: text }))}
                placeholder="Enter your email"
                placeholderTextColor={Colors.neutral.warmGray}
                keyboardType="email-address"
                autoCapitalize="none"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Phone Number</Text>
              <TextInput
                style={styles.textInput}
                value={editForm.phoneNumber}
                onChangeText={(text) => setEditForm(prev => ({ ...prev, phoneNumber: text }))}
                placeholder="Enter your phone number"
                placeholderTextColor={Colors.neutral.warmGray}
                keyboardType="phone-pad"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.inputLabel}>Country</Text>
              <ScrollView style={styles.countryList} showsVerticalScrollIndicator={false}>
                {countries.map((country) => (
                  <TouchableOpacity
                    key={country.code}
                    style={[
                      styles.countryItem,
                      editForm.countryCode === country.code && styles.selectedCountryItem
                    ]}
                    onPress={() => setEditForm(prev => ({ ...prev, countryCode: country.code }))}
                  >
                    <Text style={styles.countryFlag}>{country.flag}</Text>
                    <Text style={[
                      styles.countryName,
                      editForm.countryCode === country.code && styles.selectedCountryName
                    ]}>
                      {country.name}
                    </Text>
                    {editForm.countryCode === country.code && (
                      <Ionicons name="checkmark" size={20} color={Colors.primary.main} />
                    )}
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          </ScrollView>
        </View>
      </Modal>

      {/* Language Selection Modal */}
      <Modal
        visible={languageModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setLanguageModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setLanguageModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select Language</Text>
            <View style={{ width: 50 }} />
          </View>

          <ScrollView style={styles.modalContent}>
            {languages.map((language) => (
              <TouchableOpacity
                key={language.code}
                style={[
                  styles.optionItem,
                  (userPreferences?.preferred_language || 'en') === language.code && styles.selectedOptionItem
                ]}
                onPress={async () => {
                  await togglePreference('preferred_language', language.code);
                  setLanguageModal(false);
                }}
              >
                <Text style={styles.optionFlag}>{language.flag}</Text>
                <Text style={[
                  styles.optionName,
                  (userPreferences?.preferred_language || 'en') === language.code && styles.selectedOptionName
                ]}>
                  {language.name}
                </Text>
                {(userPreferences?.preferred_language || 'en') === language.code && (
                  <Ionicons name="checkmark" size={20} color={Colors.primary.main} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </Modal>

      {/* Currency Selection Modal */}
      <Modal
        visible={currencyModal}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={() => setCurrencyModal(false)}
      >
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={() => setCurrencyModal(false)}>
              <Text style={styles.modalCancelText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select Currency</Text>
            <View style={{ width: 50 }} />
          </View>

          <ScrollView style={styles.modalContent}>
            {currencies.map((currency) => (
              <TouchableOpacity
                key={currency.code}
                style={[
                  styles.optionItem,
                  (userPreferences?.preferred_currency || 'UGX') === currency.code && styles.selectedOptionItem
                ]}
                onPress={async () => {
                  await togglePreference('preferred_currency', currency.code);
                  setCurrencyModal(false);
                }}
              >
                <Text style={styles.optionSymbol}>{currency.symbol}</Text>
                <View style={{ flex: 1 }}>
                  <Text style={[
                    styles.optionName,
                    (userPreferences?.preferred_currency || 'UGX') === currency.code && styles.selectedOptionName
                  ]}>
                    {currency.name}
                  </Text>
                  <Text style={styles.optionCode}>{currency.code}</Text>
                </View>
                {(userPreferences?.preferred_currency || 'UGX') === currency.code && (
                  <Ionicons name="checkmark" size={20} color={Colors.primary.main} />
                )}
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      </Modal>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.neutral.cream,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.neutral.warmGray,
  },
  profileHeader: {
    paddingTop: 60,
    paddingBottom: 32,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  profileImageContainer: {
    marginBottom: 16,
  },
  imageWrapper: {
    position: 'relative',
  },
  profileImage: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 4,
    borderColor: Colors.neutral.white,
  },
  defaultAvatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.neutral.white,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 4,
    borderColor: Colors.neutral.white,
  },
  editImageButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary.dark,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.neutral.white,
  },
  profileInfo: {
    alignItems: 'center',
  },
  profileName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral.white,
    marginBottom: 4,
  },
  profilePhone: {
    fontSize: 16,
    color: Colors.neutral.white,
    opacity: 0.9,
    marginBottom: 8,
  },
  verificationBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  verificationText: {
    color: Colors.neutral.white,
    fontSize: 14,
    marginLeft: 4,
    fontWeight: '500',
  },
  quickStats: {
    flexDirection: 'row',
    backgroundColor: Colors.neutral.white,
    marginHorizontal: 24,
    marginTop: -16,
    borderRadius: 16,
    paddingVertical: 20,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
  },
  statDivider: {
    width: 1,
    backgroundColor: Colors.neutral.creamDark,
    marginVertical: 8,
  },
  section: {
    marginTop: 24,
    marginHorizontal: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 16,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.neutral.white,
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 8,
    shadowColor: Colors.shadow.light,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  menuItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 2,
  },
  menuItemSubtitle: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
  },
  logoutButton: {
    borderColor: Colors.status.error,
    borderWidth: 1,
    backgroundColor: 'rgba(192, 57, 43, 0.05)',
  },
  bottomSpacing: {
    height: 100,
  },
  // Modal Styles
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
    backgroundColor: Colors.neutral.white,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  modalCancelText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
  },
  modalSaveText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.primary.main,
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: Colors.neutral.charcoal,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
  },
  countryList: {
    maxHeight: 200,
    backgroundColor: Colors.neutral.white,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.neutral.creamDark,
  },
  countryItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  selectedCountryItem: {
    backgroundColor: Colors.primary.light + '20',
  },
  countryFlag: {
    fontSize: 20,
    marginRight: 12,
  },
  countryName: {
    flex: 1,
    fontSize: 16,
    color: Colors.neutral.charcoal,
  },
  selectedCountryName: {
    color: Colors.primary.main,
    fontWeight: '600',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
    backgroundColor: Colors.neutral.white,
  },
  selectedOptionItem: {
    backgroundColor: Colors.primary.light + '20',
  },
  optionFlag: {
    fontSize: 20,
    marginRight: 12,
  },
  optionSymbol: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.primary.main,
    marginRight: 12,
    minWidth: 40,
  },
  optionName: {
    fontSize: 16,
    color: Colors.neutral.charcoal,
    fontWeight: '500',
  },
  selectedOptionName: {
    color: Colors.primary.main,
    fontWeight: '600',
  },
  optionCode: {
    fontSize: 14,
    color: Colors.neutral.warmGray,
    marginTop: 2,
  },
});

export default ProfileScreen;
