import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';

const FAQScreen = ({ navigation }) => {
  const [expandedItems, setExpandedItems] = useState({});

  const faqData = [
    {
      id: 1,
      category: 'Money Transfer',
      icon: 'send-outline',
      color: Colors.primary.main,
      questions: [
        {
          question: 'How do I send money to another JiraniPay user?',
          answer: 'To send money: 1) Go to the Send Money section, 2) Enter the recipient\'s phone number or scan their QR code, 3) Enter the amount, 4) Add a note (optional), 5) Confirm with your PIN. The money will be transferred instantly.'
        },
        {
          question: 'What are the transfer limits?',
          answer: 'Daily limits depend on your verification level: Basic (UGX 500,000), Verified (UGX 2,000,000), Premium (UGX 10,000,000). You can increase limits by completing account verification.'
        },
        {
          question: 'How long do transfers take?',
          answer: 'JiraniPay to JiraniPay transfers are instant. Bank transfers take 1-3 business days. Mobile money transfers are usually instant but may take up to 30 minutes during peak times.'
        }
      ]
    },
    {
      id: 2,
      category: 'Bill Payments',
      icon: 'receipt-outline',
      color: Colors.secondary.savanna,
      questions: [
        {
          question: 'Which bills can I pay through JiraniPay?',
          answer: 'You can pay: Electricity (UMEME, KPLC, TANESCO), Water bills, Internet/Cable TV, School fees, Insurance premiums, Government services, and Mobile airtime/data for all major networks across East Africa.'
        },
        {
          question: 'How do I pay utility bills?',
          answer: 'Go to Bill Payments → Select utility type → Enter your account/meter number → Confirm amount → Pay with wallet balance or linked account. You\'ll receive instant confirmation and receipt.'
        },
        {
          question: 'Can I schedule recurring bill payments?',
          answer: 'Yes! When paying a bill, select "Set as Recurring" and choose frequency (weekly, monthly, quarterly). JiraniPay will automatically pay your bills on the scheduled dates.'
        }
      ]
    },
    {
      id: 3,
      category: 'Wallet Management',
      icon: 'wallet-outline',
      color: Colors.accent.coral,
      questions: [
        {
          question: 'How do I add money to my wallet?',
          answer: 'You can top up via: 1) Bank transfer (instant), 2) Mobile money (MTN, Airtel, etc.), 3) Agent locations, 4) Debit/Credit cards. Go to Wallet → Add Money and select your preferred method.'
        },
        {
          question: 'Is my money safe in JiraniPay wallet?',
          answer: 'Yes! Your funds are secured with bank-level encryption, stored in regulated financial institutions, and protected by insurance. We use multi-factor authentication and real-time fraud monitoring.'
        },
        {
          question: 'Can I withdraw money from my wallet?',
          answer: 'Yes, you can withdraw to: Your linked bank account (free), Mobile money account (small fee), or visit any JiraniPay agent location for cash withdrawal.'
        }
      ]
    },
    {
      id: 4,
      category: 'QR Code Scanner',
      icon: 'qr-code-outline',
      color: Colors.secondary.lake,
      questions: [
        {
          question: 'How do I use the QR code scanner?',
          answer: 'Tap the QR scanner icon → Point your camera at any JiraniPay QR code → The app will automatically detect and process the payment or contact information. Perfect for quick payments to merchants!'
        },
        {
          question: 'What can I do with QR codes?',
          answer: 'QR codes allow you to: Pay merchants instantly, Add contacts quickly, Receive payments (generate your own QR), Access special offers, and Join group payments or savings circles.'
        },
        {
          question: 'How do I generate my QR code?',
          answer: 'Go to Receive Money → Tap "Generate QR Code" → Set amount (optional) → Share the QR code. Others can scan it to send you money instantly without typing your phone number.'
        }
      ]
    },
    {
      id: 5,
      category: 'Account Security',
      icon: 'shield-checkmark-outline',
      color: Colors.secondary.earth,
      questions: [
        {
          question: 'How do I verify my account?',
          answer: 'Go to Profile → Account Verification → Upload: Valid ID (National ID, Passport, or Driver\'s License), Proof of address (utility bill or bank statement), and take a selfie. Verification usually takes 24-48 hours.'
        },
        {
          question: 'What security features does JiraniPay offer?',
          answer: 'We provide: Biometric login (fingerprint/face), Two-factor authentication, Transaction PINs, Real-time fraud monitoring, Account alerts, and the ability to freeze your account instantly if needed.'
        },
        {
          question: 'What should I do if I suspect fraud?',
          answer: 'Immediately: 1) Freeze your account in the app, 2) Contact support via the app or hotline, 3) Change your PIN and password, 4) Report the incident. We have 24/7 fraud monitoring and will investigate promptly.'
        }
      ]
    }
  ];

  const toggleExpanded = (categoryId, questionIndex) => {
    const key = `${categoryId}-${questionIndex}`;
    setExpandedItems(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const renderFAQCategory = (category) => (
    <View key={category.id} style={styles.categoryContainer}>
      <View style={styles.categoryHeader}>
        <View style={[styles.categoryIcon, { backgroundColor: category.color }]}>
          <Ionicons name={category.icon} size={24} color={Colors.neutral.white} />
        </View>
        <Text style={styles.categoryTitle}>{category.category}</Text>
      </View>

      {category.questions.map((item, index) => {
        const key = `${category.id}-${index}`;
        const isExpanded = expandedItems[key];

        return (
          <TouchableOpacity
            key={index}
            style={styles.questionContainer}
            onPress={() => toggleExpanded(category.id, index)}
          >
            <View style={styles.questionHeader}>
              <Text style={styles.questionText}>{item.question}</Text>
              <Ionicons
                name={isExpanded ? 'chevron-up' : 'chevron-down'}
                size={20}
                color={Colors.neutral.warmGray}
              />
            </View>
            {isExpanded && (
              <View style={styles.answerContainer}>
                <Text style={styles.answerText}>{item.answer}</Text>
              </View>
            )}
          </TouchableOpacity>
        );
      })}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={Colors.neutral.charcoal} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Frequently Asked Questions</Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.introContainer}>
          <Text style={styles.introTitle}>How can we help you?</Text>
          <Text style={styles.introText}>
            Find answers to common questions about using JiraniPay. If you can't find what you're looking for, contact our support team.
          </Text>
        </View>

        {faqData.map(renderFAQCategory)}

        <View style={styles.contactContainer}>
          <Text style={styles.contactTitle}>Still need help?</Text>
          <Text style={styles.contactText}>
            Our customer support team is available 24/7 to assist you.
          </Text>
          <TouchableOpacity style={styles.contactButton}>
            <Ionicons name="call" size={20} color={Colors.neutral.white} />
            <Text style={styles.contactButtonText}>Contact Support</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.neutral.cream,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: Colors.neutral.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  placeholder: {
    width: 40,
  },
  content: {
    flex: 1,
  },
  introContainer: {
    padding: 20,
    backgroundColor: Colors.neutral.white,
    marginBottom: 16,
  },
  introTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  introText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
    lineHeight: 24,
  },
  categoryContainer: {
    backgroundColor: Colors.neutral.white,
    marginHorizontal: 16,
    marginBottom: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  categoryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: Colors.neutral.creamLight,
  },
  categoryIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  categoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
  },
  questionContainer: {
    borderBottomWidth: 1,
    borderBottomColor: Colors.neutral.creamDark,
  },
  questionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
  },
  questionText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: Colors.neutral.charcoal,
    marginRight: 12,
  },
  answerContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  answerText: {
    fontSize: 15,
    color: Colors.neutral.warmGray,
    lineHeight: 22,
  },
  contactContainer: {
    backgroundColor: Colors.neutral.white,
    margin: 16,
    padding: 20,
    borderRadius: 12,
    alignItems: 'center',
  },
  contactTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.neutral.charcoal,
    marginBottom: 8,
  },
  contactText: {
    fontSize: 16,
    color: Colors.neutral.warmGray,
    textAlign: 'center',
    marginBottom: 16,
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary.main,
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  contactButtonText: {
    color: Colors.neutral.white,
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  bottomSpacing: {
    height: 20,
  },
});

export default FAQScreen;
