{"systemParams": "win32-x64-127", "modulesFolders": ["node_modules"], "flags": [], "linkedModules": [], "topLevelPatterns": ["@babel/core@^7.20.0", "@react-native-async-storage/async-storage@^2.2.0", "@react-native-community/datetimepicker@^8.4.1", "@react-navigation/bottom-tabs@^7.3.3", "@react-navigation/native@^7.1.10", "@react-navigation/stack@^7.3.3", "@supabase/supabase-js@^2.50.0", "expo-haptics@~13.0.1", "expo-linear-gradient@~13.0.2", "expo-local-authentication@~16.0.4", "expo-secure-store@~14.2.3", "expo-status-bar@~2.2.3", "expo@~53.0.10", "react-native-gesture-handler@~2.24.0", "react-native-reanimated@~3.17.4", "react-native-safe-area-context@5.4.0", "react-native-screens@~4.11.1", "react-native-svg@15.8.0", "react-native@0.79.3", "react@19.0.0"], "lockfileEntries": {"@0no-co/graphql.web@^1.0.5": "https://registry.npmjs.org/@0no-co/graphql.web/-/graphql.web-1.1.2.tgz", "@0no-co/graphql.web@^1.0.8": "https://registry.npmjs.org/@0no-co/graphql.web/-/graphql.web-1.1.2.tgz", "@ampproject/remapping@^2.2.0": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "@babel/code-frame@7.10.4": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz", "@babel/code-frame@^7.12.13": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/code-frame@^7.24.7": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/code-frame@^7.27.1": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "@babel/code-frame@~7.10.4": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.10.4.tgz", "@babel/compat-data@^7.22.6": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.5.tgz", "@babel/compat-data@^7.27.2": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.5.tgz", "@babel/core@^7.11.6": "https://registry.npmjs.org/@babel/core/-/core-7.27.4.tgz", "@babel/core@^7.12.3": "https://registry.npmjs.org/@babel/core/-/core-7.27.4.tgz", "@babel/core@^7.20.0": "https://registry.npmjs.org/@babel/core/-/core-7.27.4.tgz", "@babel/core@^7.25.2": "https://registry.npmjs.org/@babel/core/-/core-7.27.4.tgz", "@babel/generator@^7.20.5": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.5.tgz", "@babel/generator@^7.25.0": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.5.tgz", "@babel/generator@^7.27.3": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.5.tgz", "@babel/helper-annotate-as-pure@^7.27.1": "https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.27.3.tgz", "@babel/helper-compilation-targets@^7.22.6": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "@babel/helper-compilation-targets@^7.27.1": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "@babel/helper-compilation-targets@^7.27.2": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "@babel/helper-create-class-features-plugin@^7.27.1": "https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.27.1.tgz", "@babel/helper-create-regexp-features-plugin@^7.27.1": "https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.27.1.tgz", "@babel/helper-define-polyfill-provider@^0.6.3": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.4.tgz", "@babel/helper-define-polyfill-provider@^0.6.4": "https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.4.tgz", "@babel/helper-member-expression-to-functions@^7.27.1": "https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.27.1.tgz", "@babel/helper-module-imports@^7.25.9": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "@babel/helper-module-imports@^7.27.1": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "@babel/helper-module-transforms@^7.27.1": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "@babel/helper-module-transforms@^7.27.3": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "@babel/helper-optimise-call-expression@^7.27.1": "https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.27.1.tgz", "@babel/helper-plugin-utils@^7.0.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.10.4": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.12.13": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.14.5": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.22.5": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.27.1": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-plugin-utils@^7.8.0": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "@babel/helper-remap-async-to-generator@^7.27.1": "https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.27.1.tgz", "@babel/helper-replace-supers@^7.27.1": "https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.27.1.tgz", "@babel/helper-skip-transparent-expression-wrappers@^7.27.1": "https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.27.1.tgz", "@babel/helper-string-parser@^7.27.1": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "@babel/helper-validator-identifier@^7.25.9": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "@babel/helper-validator-identifier@^7.27.1": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "@babel/helper-validator-option@^7.27.1": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "@babel/helper-wrap-function@^7.27.1": "https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.27.1.tgz", "@babel/helpers@^7.27.4": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz", "@babel/highlight@^7.10.4": "https://registry.npmjs.org/@babel/highlight/-/highlight-7.25.9.tgz", "@babel/parser@^7.1.0": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "@babel/parser@^7.14.7": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "@babel/parser@^7.20.0": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "@babel/parser@^7.20.7": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "@babel/parser@^7.25.3": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "@babel/parser@^7.27.2": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "@babel/parser@^7.27.4": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "@babel/parser@^7.27.5": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.5.tgz", "@babel/plugin-proposal-decorators@^7.12.9": "https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.27.1.tgz", "@babel/plugin-proposal-export-default-from@^7.24.7": "https://registry.npmjs.org/@babel/plugin-proposal-export-default-from/-/plugin-proposal-export-default-from-7.27.1.tgz", "@babel/plugin-syntax-async-generators@^7.8.4": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "@babel/plugin-syntax-bigint@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz", "@babel/plugin-syntax-class-properties@^7.12.13": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "@babel/plugin-syntax-class-static-block@^7.14.5": "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "@babel/plugin-syntax-decorators@^7.27.1": "https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.27.1.tgz", "@babel/plugin-syntax-dynamic-import@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz", "@babel/plugin-syntax-export-default-from@^7.24.7": "https://registry.npmjs.org/@babel/plugin-syntax-export-default-from/-/plugin-syntax-export-default-from-7.27.1.tgz", "@babel/plugin-syntax-flow@^7.12.1": "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.27.1.tgz", "@babel/plugin-syntax-flow@^7.27.1": "https://registry.npmjs.org/@babel/plugin-syntax-flow/-/plugin-syntax-flow-7.27.1.tgz", "@babel/plugin-syntax-import-attributes@^7.24.7": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz", "@babel/plugin-syntax-import-meta@^7.10.4": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "@babel/plugin-syntax-json-strings@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "@babel/plugin-syntax-jsx@^7.27.1": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz", "@babel/plugin-syntax-logical-assignment-operators@^7.10.4": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "@babel/plugin-syntax-nullish-coalescing-operator@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "@babel/plugin-syntax-numeric-separator@^7.10.4": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "@babel/plugin-syntax-object-rest-spread@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "@babel/plugin-syntax-optional-catch-binding@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "@babel/plugin-syntax-optional-chaining@^7.8.3": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "@babel/plugin-syntax-private-property-in-object@^7.14.5": "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "@babel/plugin-syntax-top-level-await@^7.14.5": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "@babel/plugin-syntax-typescript@^7.27.1": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz", "@babel/plugin-transform-arrow-functions@^7.0.0-0": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.27.1.tgz", "@babel/plugin-transform-arrow-functions@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.27.1.tgz", "@babel/plugin-transform-async-generator-functions@^7.25.4": "https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.27.1.tgz", "@babel/plugin-transform-async-to-generator@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.27.1.tgz", "@babel/plugin-transform-block-scoping@^7.25.0": "https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.27.5.tgz", "@babel/plugin-transform-class-properties@^7.0.0-0": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.27.1.tgz", "@babel/plugin-transform-class-properties@^7.25.4": "https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.27.1.tgz", "@babel/plugin-transform-classes@^7.0.0-0": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.27.1.tgz", "@babel/plugin-transform-classes@^7.25.4": "https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.27.1.tgz", "@babel/plugin-transform-computed-properties@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.27.1.tgz", "@babel/plugin-transform-destructuring@^7.24.8": "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.27.3.tgz", "@babel/plugin-transform-destructuring@^7.27.3": "https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.27.3.tgz", "@babel/plugin-transform-export-namespace-from@^7.25.9": "https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.27.1.tgz", "@babel/plugin-transform-flow-strip-types@^7.25.2": "https://registry.npmjs.org/@babel/plugin-transform-flow-strip-types/-/plugin-transform-flow-strip-types-7.27.1.tgz", "@babel/plugin-transform-for-of@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.27.1.tgz", "@babel/plugin-transform-function-name@^7.25.1": "https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.27.1.tgz", "@babel/plugin-transform-literals@^7.25.2": "https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.27.1.tgz", "@babel/plugin-transform-logical-assignment-operators@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.27.1.tgz", "@babel/plugin-transform-modules-commonjs@^7.24.8": "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.27.1.tgz", "@babel/plugin-transform-modules-commonjs@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.27.1.tgz", "@babel/plugin-transform-named-capturing-groups-regex@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.27.1.tgz", "@babel/plugin-transform-nullish-coalescing-operator@^7.0.0-0": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.27.1.tgz", "@babel/plugin-transform-nullish-coalescing-operator@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.27.1.tgz", "@babel/plugin-transform-numeric-separator@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.27.1.tgz", "@babel/plugin-transform-object-rest-spread@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.27.3.tgz", "@babel/plugin-transform-optional-catch-binding@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.27.1.tgz", "@babel/plugin-transform-optional-chaining@^7.0.0-0": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.27.1.tgz", "@babel/plugin-transform-optional-chaining@^7.24.8": "https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.27.1.tgz", "@babel/plugin-transform-parameters@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.1.tgz", "@babel/plugin-transform-parameters@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.27.1.tgz", "@babel/plugin-transform-private-methods@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.27.1.tgz", "@babel/plugin-transform-private-property-in-object@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.27.1.tgz", "@babel/plugin-transform-react-display-name@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.27.1.tgz", "@babel/plugin-transform-react-display-name@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-react-display-name/-/plugin-transform-react-display-name-7.27.1.tgz", "@babel/plugin-transform-react-jsx-development@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-development/-/plugin-transform-react-jsx-development-7.27.1.tgz", "@babel/plugin-transform-react-jsx-self@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz", "@babel/plugin-transform-react-jsx-source@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz", "@babel/plugin-transform-react-jsx@^7.25.2": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.27.1.tgz", "@babel/plugin-transform-react-jsx@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx/-/plugin-transform-react-jsx-7.27.1.tgz", "@babel/plugin-transform-react-pure-annotations@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-react-pure-annotations/-/plugin-transform-react-pure-annotations-7.27.1.tgz", "@babel/plugin-transform-regenerator@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.27.5.tgz", "@babel/plugin-transform-runtime@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.27.4.tgz", "@babel/plugin-transform-shorthand-properties@^7.0.0-0": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.27.1.tgz", "@babel/plugin-transform-shorthand-properties@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.27.1.tgz", "@babel/plugin-transform-spread@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.27.1.tgz", "@babel/plugin-transform-sticky-regex@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.27.1.tgz", "@babel/plugin-transform-template-literals@^7.0.0-0": "https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.27.1.tgz", "@babel/plugin-transform-typescript@^7.25.2": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.27.1.tgz", "@babel/plugin-transform-typescript@^7.27.1": "https://registry.npmjs.org/@babel/plugin-transform-typescript/-/plugin-transform-typescript-7.27.1.tgz", "@babel/plugin-transform-unicode-regex@^7.0.0-0": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.27.1.tgz", "@babel/plugin-transform-unicode-regex@^7.24.7": "https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.27.1.tgz", "@babel/preset-react@^7.22.15": "https://registry.npmjs.org/@babel/preset-react/-/preset-react-7.27.1.tgz", "@babel/preset-typescript@^7.16.7": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.27.1.tgz", "@babel/preset-typescript@^7.23.0": "https://registry.npmjs.org/@babel/preset-typescript/-/preset-typescript-7.27.1.tgz", "@babel/runtime@^7.20.0": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/runtime@^7.25.0": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "@babel/template@^7.25.0": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "@babel/template@^7.27.1": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "@babel/template@^7.27.2": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "@babel/template@^7.3.3": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "@babel/traverse--for-generate-function-map@npm:@babel/traverse@^7.25.3": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.4.tgz", "@babel/traverse@^7.25.3": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.4.tgz", "@babel/traverse@^7.27.1": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.4.tgz", "@babel/traverse@^7.27.3": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.4.tgz", "@babel/traverse@^7.27.4": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.4.tgz", "@babel/types@^7.0.0": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "@babel/types@^7.20.0": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "@babel/types@^7.20.7": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "@babel/types@^7.25.2": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "@babel/types@^7.27.1": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "@babel/types@^7.27.3": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "@babel/types@^7.27.6": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "@babel/types@^7.3.3": "https://registry.npmjs.org/@babel/types/-/types-7.27.6.tgz", "@egjs/hammerjs@^2.0.17": "https://registry.npmjs.org/@egjs/hammerjs/-/hammerjs-2.0.17.tgz", "@expo/cli@0.24.14": "https://registry.npmjs.org/@expo/cli/-/cli-0.24.14.tgz", "@expo/code-signing-certificates@^0.0.5": "https://registry.npmjs.org/@expo/code-signing-certificates/-/code-signing-certificates-0.0.5.tgz", "@expo/config-plugins@~10.0.2": "https://registry.npmjs.org/@expo/config-plugins/-/config-plugins-10.0.2.tgz", "@expo/config-types@^53.0.3": "https://registry.npmjs.org/@expo/config-types/-/config-types-53.0.4.tgz", "@expo/config-types@^53.0.4": "https://registry.npmjs.org/@expo/config-types/-/config-types-53.0.4.tgz", "@expo/config@~11.0.10": "https://registry.npmjs.org/@expo/config/-/config-11.0.10.tgz", "@expo/config@~11.0.9": "https://registry.npmjs.org/@expo/config/-/config-11.0.10.tgz", "@expo/devcert@^1.1.2": "https://registry.npmjs.org/@expo/devcert/-/devcert-1.2.0.tgz", "@expo/env@~1.0.5": "https://registry.npmjs.org/@expo/env/-/env-1.0.5.tgz", "@expo/fingerprint@0.13.0": "https://registry.npmjs.org/@expo/fingerprint/-/fingerprint-0.13.0.tgz", "@expo/image-utils@^0.7.4": "https://registry.npmjs.org/@expo/image-utils/-/image-utils-0.7.4.tgz", "@expo/json-file@^9.1.4": "https://registry.npmjs.org/@expo/json-file/-/json-file-9.1.4.tgz", "@expo/json-file@~9.1.4": "https://registry.npmjs.org/@expo/json-file/-/json-file-9.1.4.tgz", "@expo/metro-config@0.20.14": "https://registry.npmjs.org/@expo/metro-config/-/metro-config-0.20.14.tgz", "@expo/metro-config@~0.20.14": "https://registry.npmjs.org/@expo/metro-config/-/metro-config-0.20.14.tgz", "@expo/osascript@^2.2.4": "https://registry.npmjs.org/@expo/osascript/-/osascript-2.2.4.tgz", "@expo/package-manager@^1.8.4": "https://registry.npmjs.org/@expo/package-manager/-/package-manager-1.8.4.tgz", "@expo/plist@^0.3.4": "https://registry.npmjs.org/@expo/plist/-/plist-0.3.4.tgz", "@expo/prebuild-config@^9.0.6": "https://registry.npmjs.org/@expo/prebuild-config/-/prebuild-config-9.0.6.tgz", "@expo/sdk-runtime-versions@^1.0.0": "https://registry.npmjs.org/@expo/sdk-runtime-versions/-/sdk-runtime-versions-1.0.0.tgz", "@expo/spawn-async@^1.7.2": "https://registry.npmjs.org/@expo/spawn-async/-/spawn-async-1.7.2.tgz", "@expo/sudo-prompt@^9.3.1": "https://registry.npmjs.org/@expo/sudo-prompt/-/sudo-prompt-9.3.2.tgz", "@expo/vector-icons@^14.0.0": "https://registry.npmjs.org/@expo/vector-icons/-/vector-icons-14.1.0.tgz", "@expo/ws-tunnel@^1.0.1": "https://registry.npmjs.org/@expo/ws-tunnel/-/ws-tunnel-1.0.6.tgz", "@expo/xcpretty@^4.3.0": "https://registry.npmjs.org/@expo/xcpretty/-/xcpretty-4.3.2.tgz", "@isaacs/cliui@^8.0.2": "https://registry.npmjs.org/@isaacs/cliui/-/cliui-8.0.2.tgz", "@isaacs/fs-minipass@^4.0.0": "https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz", "@isaacs/ttlcache@^1.4.1": "https://registry.npmjs.org/@isaacs/ttlcache/-/ttlcache-1.4.1.tgz", "@istanbuljs/load-nyc-config@^1.0.0": "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz", "@istanbuljs/schema@^0.1.2": "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz", "@jest/create-cache-key-function@^29.7.0": "https://registry.npmjs.org/@jest/create-cache-key-function/-/create-cache-key-function-29.7.0.tgz", "@jest/environment@^29.7.0": "https://registry.npmjs.org/@jest/environment/-/environment-29.7.0.tgz", "@jest/fake-timers@^29.7.0": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.7.0.tgz", "@jest/schemas@^29.6.3": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz", "@jest/transform@^29.7.0": "https://registry.npmjs.org/@jest/transform/-/transform-29.7.0.tgz", "@jest/types@^29.6.3": "https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz", "@jridgewell/gen-mapping@^0.3.2": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "@jridgewell/gen-mapping@^0.3.5": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.8.tgz", "@jridgewell/resolve-uri@^3.1.0": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "@jridgewell/set-array@^1.2.1": "https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz", "@jridgewell/source-map@^0.3.3": "https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.6.tgz", "@jridgewell/sourcemap-codec@^1.4.10": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "@jridgewell/sourcemap-codec@^1.4.14": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.0.tgz", "@jridgewell/trace-mapping@^0.3.18": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "@jridgewell/trace-mapping@^0.3.24": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "@jridgewell/trace-mapping@^0.3.25": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz", "@pkgjs/parseargs@^0.11.0": "https://registry.npmjs.org/@pkgjs/parseargs/-/parseargs-0.11.0.tgz", "@react-native-async-storage/async-storage@^2.2.0": "https://registry.npmjs.org/@react-native-async-storage/async-storage/-/async-storage-2.2.0.tgz", "@react-native-community/datetimepicker@^8.4.1": "https://registry.npmjs.org/@react-native-community/datetimepicker/-/datetimepicker-8.4.1.tgz", "@react-native/assets-registry@0.79.3": "https://registry.npmjs.org/@react-native/assets-registry/-/assets-registry-0.79.3.tgz", "@react-native/babel-plugin-codegen@0.79.3": "https://registry.npmjs.org/@react-native/babel-plugin-codegen/-/babel-plugin-codegen-0.79.3.tgz", "@react-native/babel-preset@0.79.3": "https://registry.npmjs.org/@react-native/babel-preset/-/babel-preset-0.79.3.tgz", "@react-native/codegen@0.79.3": "https://registry.npmjs.org/@react-native/codegen/-/codegen-0.79.3.tgz", "@react-native/community-cli-plugin@0.79.3": "https://registry.npmjs.org/@react-native/community-cli-plugin/-/community-cli-plugin-0.79.3.tgz", "@react-native/debugger-frontend@0.79.3": "https://registry.npmjs.org/@react-native/debugger-frontend/-/debugger-frontend-0.79.3.tgz", "@react-native/dev-middleware@0.79.3": "https://registry.npmjs.org/@react-native/dev-middleware/-/dev-middleware-0.79.3.tgz", "@react-native/gradle-plugin@0.79.3": "https://registry.npmjs.org/@react-native/gradle-plugin/-/gradle-plugin-0.79.3.tgz", "@react-native/js-polyfills@0.79.3": "https://registry.npmjs.org/@react-native/js-polyfills/-/js-polyfills-0.79.3.tgz", "@react-native/normalize-colors@0.79.2": "https://registry.npmjs.org/@react-native/normalize-colors/-/normalize-colors-0.79.2.tgz", "@react-native/normalize-colors@0.79.3": "https://registry.npmjs.org/@react-native/normalize-colors/-/normalize-colors-0.79.3.tgz", "@react-native/virtualized-lists@0.79.3": "https://registry.npmjs.org/@react-native/virtualized-lists/-/virtualized-lists-0.79.3.tgz", "@react-navigation/bottom-tabs@^7.3.3": "https://registry.yarnpkg.com/@react-navigation/bottom-tabs/-/bottom-tabs-7.3.14.tgz#9ee02baea86ab24abe267726665bc69c6df0bf4c", "@react-navigation/core@^7.10.0": "https://registry.npmjs.org/@react-navigation/core/-/core-7.10.0.tgz", "@react-navigation/elements@^2.4.3": "https://registry.npmjs.org/@react-navigation/elements/-/elements-2.4.3.tgz", "@react-navigation/native@^7.1.10": "https://registry.npmjs.org/@react-navigation/native/-/native-7.1.10.tgz", "@react-navigation/routers@^7.4.0": "https://registry.npmjs.org/@react-navigation/routers/-/routers-7.4.0.tgz", "@react-navigation/stack@^7.3.3": "https://registry.npmjs.org/@react-navigation/stack/-/stack-7.3.3.tgz", "@sinclair/typebox@^0.27.8": "https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz", "@sinonjs/commons@^3.0.0": "https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz", "@sinonjs/fake-timers@^10.0.2": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz", "@supabase/auth-js@2.70.0": "https://registry.npmjs.org/@supabase/auth-js/-/auth-js-2.70.0.tgz", "@supabase/functions-js@2.4.4": "https://registry.npmjs.org/@supabase/functions-js/-/functions-js-2.4.4.tgz", "@supabase/node-fetch@2.6.15": "https://registry.npmjs.org/@supabase/node-fetch/-/node-fetch-2.6.15.tgz", "@supabase/node-fetch@^2.6.13": "https://registry.npmjs.org/@supabase/node-fetch/-/node-fetch-2.6.13.tgz", "@supabase/node-fetch@^2.6.14": "https://registry.npmjs.org/@supabase/node-fetch/-/node-fetch-2.6.15.tgz", "@supabase/postgrest-js@1.19.4": "https://registry.npmjs.org/@supabase/postgrest-js/-/postgrest-js-1.19.4.tgz", "@supabase/realtime-js@2.11.10": "https://registry.npmjs.org/@supabase/realtime-js/-/realtime-js-2.11.10.tgz", "@supabase/storage-js@2.7.1": "https://registry.npmjs.org/@supabase/storage-js/-/storage-js-2.7.1.tgz", "@supabase/supabase-js@^2.50.0": "https://registry.npmjs.org/@supabase/supabase-js/-/supabase-js-2.50.0.tgz", "@types/babel__core@^7.1.14": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "@types/babel__generator@*": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz", "@types/babel__template@*": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz", "@types/babel__traverse@*": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "@types/babel__traverse@^7.0.6": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "@types/graceful-fs@^4.1.3": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz", "@types/hammerjs@^2.0.36": "https://registry.npmjs.org/@types/hammerjs/-/hammerjs-2.0.46.tgz", "@types/istanbul-lib-coverage@*": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "@types/istanbul-lib-coverage@^2.0.0": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "@types/istanbul-lib-report@*": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz", "@types/istanbul-reports@^3.0.0": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz", "@types/node@*": "https://registry.npmjs.org/@types/node/-/node-22.15.30.tgz", "@types/phoenix@^1.6.6": "https://registry.npmjs.org/@types/phoenix/-/phoenix-1.6.6.tgz", "@types/stack-utils@^2.0.0": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz", "@types/ws@^8.18.1": "https://registry.npmjs.org/@types/ws/-/ws-8.18.1.tgz", "@types/yargs-parser@*": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz", "@types/yargs@^17.0.8": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz", "@urql/core@^5.0.6": "https://registry.npmjs.org/@urql/core/-/core-5.1.1.tgz", "@urql/core@^5.1.1": "https://registry.npmjs.org/@urql/core/-/core-5.1.1.tgz", "@urql/exchange-retry@^1.3.0": "https://registry.npmjs.org/@urql/exchange-retry/-/exchange-retry-1.3.1.tgz", "@xmldom/xmldom@^0.8.8": "https://registry.npmjs.org/@xmldom/xmldom/-/xmldom-0.8.10.tgz", "abort-controller@^3.0.0": "https://registry.npmjs.org/abort-controller/-/abort-controller-3.0.0.tgz", "accepts@^1.3.7": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "accepts@^1.3.8": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "acorn@^8.14.0": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "agent-base@^7.1.2": "https://registry.npmjs.org/agent-base/-/agent-base-7.1.3.tgz", "anser@^1.4.9": "https://registry.npmjs.org/anser/-/anser-1.4.10.tgz", "ansi-escapes@^4.2.1": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "ansi-regex@^4.1.0": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-4.1.1.tgz", "ansi-regex@^5.0.0": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-regex@^5.0.1": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "ansi-regex@^6.0.1": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-6.1.0.tgz", "ansi-styles@^3.2.1": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz", "ansi-styles@^4.0.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^4.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "ansi-styles@^5.0.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz", "ansi-styles@^6.1.0": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-6.2.1.tgz", "any-promise@^1.0.0": "https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz", "anymatch@^3.0.3": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "arg@^5.0.2": "https://registry.npmjs.org/arg/-/arg-5.0.2.tgz", "argparse@^1.0.7": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "argparse@^2.0.1": "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz", "asap@~2.0.6": "https://registry.npmjs.org/asap/-/asap-2.0.6.tgz", "async-limiter@~1.0.0": "https://registry.npmjs.org/async-limiter/-/async-limiter-1.0.1.tgz", "babel-jest@^29.7.0": "https://registry.npmjs.org/babel-jest/-/babel-jest-29.7.0.tgz", "babel-plugin-istanbul@^6.1.1": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "babel-plugin-jest-hoist@^29.6.3": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz", "babel-plugin-polyfill-corejs2@^0.4.10": "https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.13.tgz", "babel-plugin-polyfill-corejs3@^0.11.0": "https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.11.1.tgz", "babel-plugin-polyfill-regenerator@^0.6.1": "https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.4.tgz", "babel-plugin-react-native-web@~0.19.13": "https://registry.npmjs.org/babel-plugin-react-native-web/-/babel-plugin-react-native-web-0.19.13.tgz", "babel-plugin-syntax-hermes-parser@0.25.1": "https://registry.npmjs.org/babel-plugin-syntax-hermes-parser/-/babel-plugin-syntax-hermes-parser-0.25.1.tgz", "babel-plugin-syntax-hermes-parser@^0.25.1": "https://registry.npmjs.org/babel-plugin-syntax-hermes-parser/-/babel-plugin-syntax-hermes-parser-0.25.1.tgz", "babel-plugin-transform-flow-enums@^0.0.2": "https://registry.npmjs.org/babel-plugin-transform-flow-enums/-/babel-plugin-transform-flow-enums-0.0.2.tgz", "babel-preset-current-node-syntax@^1.0.0": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz", "babel-preset-expo@~13.2.0": "https://registry.npmjs.org/babel-preset-expo/-/babel-preset-expo-13.2.0.tgz", "babel-preset-jest@^29.6.3": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz", "balanced-match@^1.0.0": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "base64-js@^1.2.3": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "base64-js@^1.3.1": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "base64-js@^1.5.1": "https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz", "better-opn@~3.0.2": "https://registry.npmjs.org/better-opn/-/better-opn-3.0.2.tgz", "big-integer@1.6.x": "https://registry.npmjs.org/big-integer/-/big-integer-1.6.52.tgz", "boolbase@^1.0.0": "https://registry.yarnpkg.com/boolbase/-/boolbase-1.0.0.tgz#68dff5fbe60c51eb37725ea9e3ed310dcc1e776e", "bplist-creator@0.1.0": "https://registry.npmjs.org/bplist-creator/-/bplist-creator-0.1.0.tgz", "bplist-parser@0.3.1": "https://registry.npmjs.org/bplist-parser/-/bplist-parser-0.3.1.tgz", "bplist-parser@^0.3.1": "https://registry.npmjs.org/bplist-parser/-/bplist-parser-0.3.2.tgz", "brace-expansion@^1.1.7": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz", "brace-expansion@^2.0.1": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.1.tgz", "braces@^3.0.3": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "browserslist@^4.24.0": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.0.tgz", "browserslist@^4.25.0": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.0.tgz", "bser@2.1.1": "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz", "buffer-from@^1.0.0": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "buffer@^5.4.3": "https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz", "bytes@3.1.2": "https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz", "caller-callsite@^2.0.0": "https://registry.npmjs.org/caller-callsite/-/caller-callsite-2.0.0.tgz", "caller-path@^2.0.0": "https://registry.npmjs.org/caller-path/-/caller-path-2.0.0.tgz", "callsites@^2.0.0": "https://registry.npmjs.org/callsites/-/callsites-2.0.0.tgz", "camelcase@^5.3.1": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "camelcase@^6.2.0": "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz", "caniuse-lite@^1.0.30001718": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001721.tgz", "chalk@^2.0.1": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "chalk@^2.4.2": "https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz", "chalk@^4.0.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.0": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chalk@^4.1.2": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "chownr@^3.0.0": "https://registry.npmjs.org/chownr/-/chownr-3.0.0.tgz", "chrome-launcher@^0.15.2": "https://registry.npmjs.org/chrome-launcher/-/chrome-launcher-0.15.2.tgz", "chromium-edge-launcher@^0.2.0": "https://registry.npmjs.org/chromium-edge-launcher/-/chromium-edge-launcher-0.2.0.tgz", "ci-info@^2.0.0": "https://registry.npmjs.org/ci-info/-/ci-info-2.0.0.tgz", "ci-info@^3.2.0": "https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz", "ci-info@^3.3.0": "https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz", "cli-cursor@^2.1.0": "https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz", "cli-spinners@^2.0.0": "https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz", "cliui@^8.0.1": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "clone@^1.0.2": "https://registry.npmjs.org/clone/-/clone-1.0.4.tgz", "color-convert@^1.9.0": "https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz", "color-convert@^2.0.1": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "color-name@1.1.3": "https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz", "color-name@^1.0.0": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "color-name@~1.1.4": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "color-string@^1.9.0": "https://registry.npmjs.org/color-string/-/color-string-1.9.1.tgz", "color@^4.2.3": "https://registry.npmjs.org/color/-/color-4.2.3.tgz", "commander@^12.0.0": "https://registry.npmjs.org/commander/-/commander-12.1.0.tgz", "commander@^2.20.0": "https://registry.npmjs.org/commander/-/commander-2.20.3.tgz", "commander@^4.0.0": "https://registry.npmjs.org/commander/-/commander-4.1.1.tgz", "commander@^7.2.0": "https://registry.npmjs.org/commander/-/commander-7.2.0.tgz", "compressible@~2.0.18": "https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz", "compression@^1.7.4": "https://registry.npmjs.org/compression/-/compression-1.8.0.tgz", "concat-map@0.0.1": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "connect@^3.6.5": "https://registry.npmjs.org/connect/-/connect-3.7.0.tgz", "connect@^3.7.0": "https://registry.npmjs.org/connect/-/connect-3.7.0.tgz", "convert-source-map@^2.0.0": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "core-js-compat@^3.40.0": "https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.43.0.tgz", "cosmiconfig@^5.0.5": "https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-5.2.1.tgz", "cross-spawn@^7.0.3": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "cross-spawn@^7.0.6": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "crypto-random-string@^2.0.0": "https://registry.npmjs.org/crypto-random-string/-/crypto-random-string-2.0.0.tgz", "css-select@^5.1.0": "https://registry.yarnpkg.com/css-select/-/css-select-5.1.0.tgz#b8ebd6554c3637ccc76688804ad3f6a6fdaea8a6", "css-tree@^1.1.3": "https://registry.yarnpkg.com/css-tree/-/css-tree-1.1.3.tgz#eb4870fb6fd7707327ec95c2ff2ab09b5e8db91d", "css-what@^6.1.0": "https://registry.yarnpkg.com/css-what/-/css-what-6.1.0.tgz#fb5effcf76f1ddea2c81bdfaa4de44e79bac70f4", "debug@2.6.9": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@4": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^2.2.0": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@^2.6.9": "https://registry.npmjs.org/debug/-/debug-2.6.9.tgz", "debug@^3.1.0": "https://registry.npmjs.org/debug/-/debug-3.2.7.tgz", "debug@^4.1.0": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.1.1": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.1": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.2": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.4": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.3.5": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "debug@^4.4.0": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "decode-uri-component@^0.2.2": "https://registry.npmjs.org/decode-uri-component/-/decode-uri-component-0.2.2.tgz", "deep-extend@^0.6.0": "https://registry.npmjs.org/deep-extend/-/deep-extend-0.6.0.tgz", "deepmerge@^4.3.1": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz", "defaults@^1.0.3": "https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz", "define-lazy-prop@^2.0.0": "https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz", "depd@2.0.0": "https://registry.npmjs.org/depd/-/depd-2.0.0.tgz", "destroy@1.2.0": "https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz", "detect-libc@^1.0.3": "https://registry.npmjs.org/detect-libc/-/detect-libc-1.0.3.tgz", "dom-serializer@^2.0.0": "https://registry.yarnpkg.com/dom-serializer/-/dom-serializer-2.0.0.tgz#e41b802e1eedf9f6cae183ce5e622d789d7d8e53", "domelementtype@^2.3.0": "https://registry.yarnpkg.com/domelementtype/-/domelementtype-2.3.0.tgz#5c45e8e869952626331d7aab326d01daf65d589d", "domhandler@^5.0.2": "https://registry.yarnpkg.com/domhandler/-/domhandler-5.0.3.tgz#cc385f7f751f1d1fc650c21374804254538c7d31", "domhandler@^5.0.3": "https://registry.yarnpkg.com/domhandler/-/domhandler-5.0.3.tgz#cc385f7f751f1d1fc650c21374804254538c7d31", "domutils@^3.0.1": "https://registry.yarnpkg.com/domutils/-/domutils-3.2.2.tgz#edbfe2b668b0c1d97c24baf0f1062b132221bc78", "dotenv-expand@~11.0.6": "https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-11.0.7.tgz", "dotenv@^16.4.5": "https://registry.npmjs.org/dotenv/-/dotenv-16.5.0.tgz", "dotenv@~16.4.5": "https://registry.npmjs.org/dotenv/-/dotenv-16.4.7.tgz", "eastasianwidth@^0.2.0": "https://registry.npmjs.org/eastasianwidth/-/eastasianwidth-0.2.0.tgz", "ee-first@1.1.1": "https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz", "electron-to-chromium@^1.5.160": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.166.tgz", "emoji-regex@^8.0.0": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "emoji-regex@^9.2.2": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-9.2.2.tgz", "encodeurl@~1.0.2": "https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz", "encodeurl@~2.0.0": "https://registry.npmjs.org/encodeurl/-/encodeurl-2.0.0.tgz", "entities@^4.2.0": "https://registry.yarnpkg.com/entities/-/entities-4.5.0.tgz#5d268ea5e7113ec74c4d033b79ea5a35a488fb48", "env-editor@^0.4.1": "https://registry.npmjs.org/env-editor/-/env-editor-0.4.2.tgz", "error-ex@^1.3.1": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz", "error-stack-parser@^2.0.6": "https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz", "escalade@^3.1.1": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "escalade@^3.2.0": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "escape-html@~1.0.3": "https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz", "escape-string-regexp@^1.0.5": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz", "escape-string-regexp@^2.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "escape-string-regexp@^4.0.0": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz", "esprima@^4.0.0": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "etag@~1.8.1": "https://registry.npmjs.org/etag/-/etag-1.8.1.tgz", "event-target-shim@^5.0.0": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz", "event-target-shim@^5.0.1": "https://registry.npmjs.org/event-target-shim/-/event-target-shim-5.0.1.tgz", "exec-async@^2.2.0": "https://registry.npmjs.org/exec-async/-/exec-async-2.2.0.tgz", "expo-asset@~11.1.5": "https://registry.npmjs.org/expo-asset/-/expo-asset-11.1.5.tgz", "expo-constants@~17.1.5": "https://registry.npmjs.org/expo-constants/-/expo-constants-17.1.6.tgz", "expo-constants@~17.1.6": "https://registry.npmjs.org/expo-constants/-/expo-constants-17.1.6.tgz", "expo-file-system@~18.1.10": "https://registry.npmjs.org/expo-file-system/-/expo-file-system-18.1.10.tgz", "expo-font@~13.3.1": "https://registry.npmjs.org/expo-font/-/expo-font-13.3.1.tgz", "expo-haptics@~13.0.1": "https://registry.yarnpkg.com/expo-haptics/-/expo-haptics-13.0.1.tgz#35679c7fde4ae1c21ae3bc2d2cb34c266049dc2c", "expo-keep-awake@~14.1.4": "https://registry.npmjs.org/expo-keep-awake/-/expo-keep-awake-14.1.4.tgz", "expo-linear-gradient@~13.0.2": "https://registry.yarnpkg.com/expo-linear-gradient/-/expo-linear-gradient-13.0.2.tgz#21bd7bc7c71ef4f7c089521daa16db729d2aec5f", "expo-local-authentication@~16.0.4": "https://registry.npmjs.org/expo-local-authentication/-/expo-local-authentication-16.0.4.tgz", "expo-modules-autolinking@2.1.11": "https://registry.npmjs.org/expo-modules-autolinking/-/expo-modules-autolinking-2.1.11.tgz", "expo-modules-core@2.4.0": "https://registry.npmjs.org/expo-modules-core/-/expo-modules-core-2.4.0.tgz", "expo-secure-store@~14.2.3": "https://registry.npmjs.org/expo-secure-store/-/expo-secure-store-14.2.3.tgz", "expo-status-bar@~2.2.3": "https://registry.npmjs.org/expo-status-bar/-/expo-status-bar-2.2.3.tgz", "expo@~53.0.10": "https://registry.npmjs.org/expo/-/expo-53.0.11.tgz", "exponential-backoff@^3.1.1": "https://registry.npmjs.org/exponential-backoff/-/exponential-backoff-3.1.2.tgz", "fast-deep-equal@^3.1.3": "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz", "fast-json-stable-stringify@^2.1.0": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "fb-watchman@^2.0.0": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz", "fill-range@^7.1.1": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "filter-obj@^1.1.0": "https://registry.npmjs.org/filter-obj/-/filter-obj-1.1.0.tgz", "finalhandler@1.1.2": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz", "find-up@^4.1.0": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "find-up@^5.0.0": "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz", "flow-enums-runtime@^0.0.6": "https://registry.npmjs.org/flow-enums-runtime/-/flow-enums-runtime-0.0.6.tgz", "fontfaceobserver@^2.1.0": "https://registry.npmjs.org/fontfaceobserver/-/fontfaceobserver-2.3.0.tgz", "foreground-child@^3.1.0": "https://registry.npmjs.org/foreground-child/-/foreground-child-3.3.1.tgz", "freeport-async@^2.0.0": "https://registry.npmjs.org/freeport-async/-/freeport-async-2.0.0.tgz", "fresh@0.5.2": "https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz", "fs.realpath@^1.0.0": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "fsevents@^2.3.2": "https://registry.yarnpkg.com/fsevents/-/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6", "function-bind@^1.1.2": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "gensync@^1.0.0-beta.2": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "get-caller-file@^2.0.5": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "get-package-type@^0.1.0": "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz", "getenv@^1.0.0": "https://registry.npmjs.org/getenv/-/getenv-1.0.0.tgz", "getenv@^2.0.0": "https://registry.npmjs.org/getenv/-/getenv-2.0.0.tgz", "glob@^10.3.10": "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz", "glob@^10.4.2": "https://registry.npmjs.org/glob/-/glob-10.4.5.tgz", "glob@^7.1.1": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.1.3": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "glob@^7.1.4": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "globals@^11.1.0": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "graceful-fs@^4.2.4": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "graceful-fs@^4.2.9": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "has-flag@^3.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz", "has-flag@^4.0.0": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "hasown@^2.0.2": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "hermes-estree@0.25.1": "https://registry.npmjs.org/hermes-estree/-/hermes-estree-0.25.1.tgz", "hermes-estree@0.28.1": "https://registry.npmjs.org/hermes-estree/-/hermes-estree-0.28.1.tgz", "hermes-parser@0.25.1": "https://registry.npmjs.org/hermes-parser/-/hermes-parser-0.25.1.tgz", "hermes-parser@0.28.1": "https://registry.npmjs.org/hermes-parser/-/hermes-parser-0.28.1.tgz", "hoist-non-react-statics@^3.3.0": "https://registry.npmjs.org/hoist-non-react-statics/-/hoist-non-react-statics-3.3.2.tgz", "hosted-git-info@^7.0.0": "https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-7.0.2.tgz", "http-errors@2.0.0": "https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz", "https-proxy-agent@^7.0.5": "https://registry.npmjs.org/https-proxy-agent/-/https-proxy-agent-7.0.6.tgz", "ieee754@^1.1.13": "https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz", "ignore@^5.3.1": "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz", "image-size@^1.0.2": "https://registry.npmjs.org/image-size/-/image-size-1.2.1.tgz", "import-fresh@^2.0.0": "https://registry.npmjs.org/import-fresh/-/import-fresh-2.0.0.tgz", "imurmurhash@^0.1.4": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "inflight@^1.0.4": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "inherits@2": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@2.0.4": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "inherits@~2.0.3": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "ini@~1.3.0": "https://registry.npmjs.org/ini/-/ini-1.3.8.tgz", "invariant@^2.2.4": "https://registry.npmjs.org/invariant/-/invariant-2.2.4.tgz", "is-arrayish@^0.2.1": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "is-arrayish@^0.3.1": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.3.2.tgz", "is-core-module@^2.16.0": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "is-directory@^0.3.1": "https://registry.npmjs.org/is-directory/-/is-directory-0.3.1.tgz", "is-docker@^2.0.0": "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz", "is-docker@^2.1.1": "https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz", "is-fullwidth-code-point@^3.0.0": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "is-number@^7.0.0": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "is-plain-obj@^2.1.0": "https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-2.1.0.tgz", "is-wsl@^2.1.1": "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz", "is-wsl@^2.2.0": "https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz", "isexe@^2.0.0": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "istanbul-lib-coverage@^3.2.0": "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz", "istanbul-lib-instrument@^5.0.4": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz", "jackspeak@^3.1.2": "https://registry.npmjs.org/jackspeak/-/jackspeak-3.4.3.tgz", "jest-environment-node@^29.7.0": "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-29.7.0.tgz", "jest-get-type@^29.6.3": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz", "jest-haste-map@^29.7.0": "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-29.7.0.tgz", "jest-message-util@^29.7.0": "https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.7.0.tgz", "jest-mock@^29.7.0": "https://registry.npmjs.org/jest-mock/-/jest-mock-29.7.0.tgz", "jest-regex-util@^29.6.3": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz", "jest-util@^29.7.0": "https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz", "jest-validate@^29.7.0": "https://registry.npmjs.org/jest-validate/-/jest-validate-29.7.0.tgz", "jest-worker@^29.7.0": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.7.0.tgz", "jimp-compact@0.16.1": "https://registry.npmjs.org/jimp-compact/-/jimp-compact-0.16.1.tgz", "js-tokens@^3.0.0 || ^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-tokens@^4.0.0": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "js-yaml@^3.13.1": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz", "js-yaml@^4.1.0": "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz", "jsc-safe-url@^0.2.2": "https://registry.npmjs.org/jsc-safe-url/-/jsc-safe-url-0.2.4.tgz", "jsc-safe-url@^0.2.4": "https://registry.npmjs.org/jsc-safe-url/-/jsc-safe-url-0.2.4.tgz", "jsesc@^3.0.2": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "jsesc@~3.0.2": "https://registry.npmjs.org/jsesc/-/jsesc-3.0.2.tgz", "json-parse-better-errors@^1.0.1": "https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz", "json5@^2.2.3": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "kleur@^3.0.3": "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz", "lan-network@^0.1.6": "https://registry.npmjs.org/lan-network/-/lan-network-0.1.7.tgz", "leven@^3.1.0": "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz", "lighthouse-logger@^1.0.0": "https://registry.npmjs.org/lighthouse-logger/-/lighthouse-logger-1.4.2.tgz", "lightningcss-darwin-arm64@1.27.0": "https://registry.yarnpkg.com/lightningcss-darwin-arm64/-/lightningcss-darwin-arm64-1.27.0.tgz#565bd610533941cba648a70e105987578d82f996", "lightningcss-darwin-x64@1.27.0": "https://registry.yarnpkg.com/lightningcss-darwin-x64/-/lightningcss-darwin-x64-1.27.0.tgz#c906a267237b1c7fe08bff6c5ac032c099bc9482", "lightningcss-freebsd-x64@1.27.0": "https://registry.yarnpkg.com/lightningcss-freebsd-x64/-/lightningcss-freebsd-x64-1.27.0.tgz#a7c3c4d6ee18dffeb8fa69f14f8f9267f7dc0c34", "lightningcss-linux-arm-gnueabihf@1.27.0": "https://registry.yarnpkg.com/lightningcss-linux-arm-gnueabihf/-/lightningcss-linux-arm-gnueabihf-1.27.0.tgz#c7c16432a571ec877bf734fe500e4a43d48c2814", "lightningcss-linux-arm64-gnu@1.27.0": "https://registry.yarnpkg.com/lightningcss-linux-arm64-gnu/-/lightningcss-linux-arm64-gnu-1.27.0.tgz#cfd9e18df1cd65131da286ddacfa3aee6862a752", "lightningcss-linux-arm64-musl@1.27.0": "https://registry.yarnpkg.com/lightningcss-linux-arm64-musl/-/lightningcss-linux-arm64-musl-1.27.0.tgz#6682ff6b9165acef9a6796bd9127a8e1247bb0ed", "lightningcss-linux-x64-gnu@1.27.0": "https://registry.yarnpkg.com/lightningcss-linux-x64-gnu/-/lightningcss-linux-x64-gnu-1.27.0.tgz#714221212ad184ddfe974bbb7dbe9300dfde4bc0", "lightningcss-linux-x64-musl@1.27.0": "https://registry.yarnpkg.com/lightningcss-linux-x64-musl/-/lightningcss-linux-x64-musl-1.27.0.tgz#247958daf622a030a6dc2285afa16b7184bdf21e", "lightningcss-win32-arm64-msvc@1.27.0": "https://registry.yarnpkg.com/lightningcss-win32-arm64-msvc/-/lightningcss-win32-arm64-msvc-1.27.0.tgz#64cfe473c264ef5dc275a4d57a516d77fcac6bc9", "lightningcss-win32-x64-msvc@1.27.0": "https://registry.npmjs.org/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.27.0.tgz", "lightningcss@~1.27.0": "https://registry.npmjs.org/lightningcss/-/lightningcss-1.27.0.tgz", "lines-and-columns@^1.1.6": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "locate-path@^5.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "locate-path@^6.0.0": "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz", "lodash.debounce@^4.0.8": "https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz", "lodash.throttle@^4.1.1": "https://registry.npmjs.org/lodash.throttle/-/lodash.throttle-4.1.1.tgz", "log-symbols@^2.2.0": "https://registry.npmjs.org/log-symbols/-/log-symbols-2.2.0.tgz", "loose-envify@^1.0.0": "https://registry.npmjs.org/loose-envify/-/loose-envify-1.4.0.tgz", "lru-cache@^10.0.1": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz", "lru-cache@^10.2.0": "https://registry.npmjs.org/lru-cache/-/lru-cache-10.4.3.tgz", "lru-cache@^5.1.1": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "makeerror@1.0.12": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz", "marky@^1.2.2": "https://registry.npmjs.org/marky/-/marky-1.3.0.tgz", "mdn-data@2.0.14": "https://registry.yarnpkg.com/mdn-data/-/mdn-data-2.0.14.tgz#7113fc4281917d63ce29b43446f701e68c25ba50", "memoize-one@^5.0.0": "https://registry.npmjs.org/memoize-one/-/memoize-one-5.2.1.tgz", "merge-options@^3.0.4": "https://registry.npmjs.org/merge-options/-/merge-options-3.0.4.tgz", "merge-stream@^2.0.0": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz", "metro-babel-transformer@0.82.4": "https://registry.npmjs.org/metro-babel-transformer/-/metro-babel-transformer-0.82.4.tgz", "metro-cache-key@0.82.4": "https://registry.npmjs.org/metro-cache-key/-/metro-cache-key-0.82.4.tgz", "metro-cache@0.82.4": "https://registry.npmjs.org/metro-cache/-/metro-cache-0.82.4.tgz", "metro-config@0.82.4": "https://registry.npmjs.org/metro-config/-/metro-config-0.82.4.tgz", "metro-config@^0.82.0": "https://registry.npmjs.org/metro-config/-/metro-config-0.82.4.tgz", "metro-core@0.82.4": "https://registry.npmjs.org/metro-core/-/metro-core-0.82.4.tgz", "metro-core@^0.82.0": "https://registry.npmjs.org/metro-core/-/metro-core-0.82.4.tgz", "metro-file-map@0.82.4": "https://registry.npmjs.org/metro-file-map/-/metro-file-map-0.82.4.tgz", "metro-minify-terser@0.82.4": "https://registry.npmjs.org/metro-minify-terser/-/metro-minify-terser-0.82.4.tgz", "metro-resolver@0.82.4": "https://registry.npmjs.org/metro-resolver/-/metro-resolver-0.82.4.tgz", "metro-runtime@0.82.4": "https://registry.npmjs.org/metro-runtime/-/metro-runtime-0.82.4.tgz", "metro-runtime@^0.82.0": "https://registry.npmjs.org/metro-runtime/-/metro-runtime-0.82.4.tgz", "metro-source-map@0.82.4": "https://registry.npmjs.org/metro-source-map/-/metro-source-map-0.82.4.tgz", "metro-source-map@^0.82.0": "https://registry.npmjs.org/metro-source-map/-/metro-source-map-0.82.4.tgz", "metro-symbolicate@0.82.4": "https://registry.npmjs.org/metro-symbolicate/-/metro-symbolicate-0.82.4.tgz", "metro-transform-plugins@0.82.4": "https://registry.npmjs.org/metro-transform-plugins/-/metro-transform-plugins-0.82.4.tgz", "metro-transform-worker@0.82.4": "https://registry.npmjs.org/metro-transform-worker/-/metro-transform-worker-0.82.4.tgz", "metro@0.82.4": "https://registry.npmjs.org/metro/-/metro-0.82.4.tgz", "metro@^0.82.0": "https://registry.npmjs.org/metro/-/metro-0.82.4.tgz", "micromatch@^4.0.4": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "mime-db@1.52.0": "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz", "mime-db@>= 1.43.0 < 2": "https://registry.npmjs.org/mime-db/-/mime-db-1.54.0.tgz", "mime-types@^2.1.27": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime-types@~2.1.34": "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz", "mime@1.6.0": "https://registry.npmjs.org/mime/-/mime-1.6.0.tgz", "mimic-fn@^1.0.0": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz", "minimatch@^3.0.4": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^3.1.1": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "minimatch@^9.0.0": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz", "minimatch@^9.0.4": "https://registry.npmjs.org/minimatch/-/minimatch-9.0.5.tgz", "minimist@^1.2.0": "https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz", "minipass@^5.0.0 || ^6.0.2 || ^7.0.0": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "minipass@^7.0.4": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "minipass@^7.1.2": "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz", "minizlib@^3.0.1": "https://registry.npmjs.org/minizlib/-/minizlib-3.0.2.tgz", "mkdirp@^1.0.4": "https://registry.npmjs.org/mkdirp/-/mkdirp-1.0.4.tgz", "mkdirp@^3.0.1": "https://registry.npmjs.org/mkdirp/-/mkdirp-3.0.1.tgz", "ms@2.0.0": "https://registry.npmjs.org/ms/-/ms-2.0.0.tgz", "ms@2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.1.1": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "ms@^2.1.3": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "mz@^2.7.0": "https://registry.npmjs.org/mz/-/mz-2.7.0.tgz", "nanoid@^3.3.11": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "nanoid@^3.3.7": "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz", "negotiator@0.6.3": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz", "negotiator@~0.6.4": "https://registry.npmjs.org/negotiator/-/negotiator-0.6.4.tgz", "nested-error-stacks@~2.0.1": "https://registry.npmjs.org/nested-error-stacks/-/nested-error-stacks-2.0.1.tgz", "node-forge@^1.2.1": "https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz", "node-forge@^1.3.1": "https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz", "node-int64@^0.4.0": "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz", "node-releases@^2.0.19": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "normalize-path@^3.0.0": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "npm-package-arg@^11.0.0": "https://registry.npmjs.org/npm-package-arg/-/npm-package-arg-11.0.3.tgz", "nth-check@^2.0.1": "https://registry.yarnpkg.com/nth-check/-/nth-check-2.1.1.tgz#c9eab428effce36cd6b92c924bdb000ef1f1ed1d", "nullthrows@^1.1.1": "https://registry.npmjs.org/nullthrows/-/nullthrows-1.1.1.tgz", "ob1@0.82.4": "https://registry.npmjs.org/ob1/-/ob1-0.82.4.tgz", "object-assign@^4.0.1": "https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz", "on-finished@2.4.1": "https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz", "on-finished@~2.3.0": "https://registry.npmjs.org/on-finished/-/on-finished-2.3.0.tgz", "on-headers@~1.0.2": "https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz", "once@^1.3.0": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "onetime@^2.0.0": "https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz", "open@^7.0.3": "https://registry.npmjs.org/open/-/open-7.4.2.tgz", "open@^8.0.4": "https://registry.npmjs.org/open/-/open-8.4.2.tgz", "ora@^3.4.0": "https://registry.npmjs.org/ora/-/ora-3.4.0.tgz", "p-limit@^2.2.0": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "p-limit@^3.0.2": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "p-limit@^3.1.0": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "p-locate@^4.1.0": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "p-locate@^5.0.0": "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz", "p-try@^2.0.0": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "package-json-from-dist@^1.0.0": "https://registry.npmjs.org/package-json-from-dist/-/package-json-from-dist-1.0.1.tgz", "parse-json@^4.0.0": "https://registry.npmjs.org/parse-json/-/parse-json-4.0.0.tgz", "parse-png@^2.1.0": "https://registry.npmjs.org/parse-png/-/parse-png-2.1.0.tgz", "parseurl@~1.3.3": "https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz", "path-exists@^4.0.0": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "path-is-absolute@^1.0.0": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "path-key@^3.1.0": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "path-parse@^1.0.5": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "path-parse@^1.0.7": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "path-scurry@^1.11.1": "https://registry.npmjs.org/path-scurry/-/path-scurry-1.11.1.tgz", "picocolors@^1.0.0": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "picocolors@^1.1.1": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "picomatch@^2.0.4": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.2.3": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^2.3.1": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "picomatch@^3.0.1": "https://registry.npmjs.org/picomatch/-/picomatch-3.0.1.tgz", "pirates@^4.0.1": "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz", "pirates@^4.0.4": "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz", "plist@^3.0.5": "https://registry.npmjs.org/plist/-/plist-3.1.0.tgz", "pngjs@^3.3.0": "https://registry.npmjs.org/pngjs/-/pngjs-3.4.0.tgz", "postcss@~8.4.32": "https://registry.npmjs.org/postcss/-/postcss-8.4.49.tgz", "pretty-bytes@^5.6.0": "https://registry.npmjs.org/pretty-bytes/-/pretty-bytes-5.6.0.tgz", "pretty-format@^29.7.0": "https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz", "proc-log@^4.0.0": "https://registry.npmjs.org/proc-log/-/proc-log-4.2.0.tgz", "progress@^2.0.3": "https://registry.npmjs.org/progress/-/progress-2.0.3.tgz", "promise@^8.3.0": "https://registry.npmjs.org/promise/-/promise-8.3.0.tgz", "prompts@^2.3.2": "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz", "punycode@^2.1.1": "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz", "qrcode-terminal@0.11.0": "https://registry.npmjs.org/qrcode-terminal/-/qrcode-terminal-0.11.0.tgz", "query-string@^7.1.3": "https://registry.npmjs.org/query-string/-/query-string-7.1.3.tgz", "queue@6.0.2": "https://registry.npmjs.org/queue/-/queue-6.0.2.tgz", "range-parser@~1.2.1": "https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz", "rc@~1.2.7": "https://registry.npmjs.org/rc/-/rc-1.2.8.tgz", "react-devtools-core@^6.1.1": "https://registry.npmjs.org/react-devtools-core/-/react-devtools-core-6.1.2.tgz", "react-freeze@^1.0.0": "https://registry.npmjs.org/react-freeze/-/react-freeze-1.0.4.tgz", "react-is@^16.7.0": "https://registry.npmjs.org/react-is/-/react-is-16.13.1.tgz", "react-is@^18.0.0": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz", "react-is@^19.1.0": "https://registry.npmjs.org/react-is/-/react-is-19.1.0.tgz", "react-native-edge-to-edge@1.6.0": "https://registry.npmjs.org/react-native-edge-to-edge/-/react-native-edge-to-edge-1.6.0.tgz", "react-native-gesture-handler@~2.24.0": "https://registry.npmjs.org/react-native-gesture-handler/-/react-native-gesture-handler-2.24.0.tgz", "react-native-is-edge-to-edge@1.1.7": "https://registry.npmjs.org/react-native-is-edge-to-edge/-/react-native-is-edge-to-edge-1.1.7.tgz", "react-native-is-edge-to-edge@^1.1.6": "https://registry.npmjs.org/react-native-is-edge-to-edge/-/react-native-is-edge-to-edge-1.1.7.tgz", "react-native-is-edge-to-edge@^1.1.7": "https://registry.npmjs.org/react-native-is-edge-to-edge/-/react-native-is-edge-to-edge-1.1.7.tgz", "react-native-reanimated@~3.17.4": "https://registry.npmjs.org/react-native-reanimated/-/react-native-reanimated-3.17.5.tgz", "react-native-safe-area-context@5.4.0": "https://registry.npmjs.org/react-native-safe-area-context/-/react-native-safe-area-context-5.4.0.tgz", "react-native-screens@~4.11.1": "https://registry.npmjs.org/react-native-screens/-/react-native-screens-4.11.1.tgz", "react-native-svg@15.8.0": "https://registry.yarnpkg.com/react-native-svg/-/react-native-svg-15.8.0.tgz#9b5fd4f5cf5675197b3f4cbfcc77c215de2b9502", "react-native@0.79.3": "https://registry.npmjs.org/react-native/-/react-native-0.79.3.tgz", "react-refresh@^0.14.0": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.2.tgz", "react-refresh@^0.14.2": "https://registry.npmjs.org/react-refresh/-/react-refresh-0.14.2.tgz", "react@19.0.0": "https://registry.npmjs.org/react/-/react-19.0.0.tgz", "regenerate-unicode-properties@^10.2.0": "https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.2.0.tgz", "regenerate@^1.4.2": "https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz", "regenerator-runtime@^0.13.2": "https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.13.11.tgz", "regexpu-core@^6.2.0": "https://registry.npmjs.org/regexpu-core/-/regexpu-core-6.2.0.tgz", "regjsgen@^0.8.0": "https://registry.npmjs.org/regjsgen/-/regjsgen-0.8.0.tgz", "regjsparser@^0.12.0": "https://registry.npmjs.org/regjsparser/-/regjsparser-0.12.0.tgz", "require-directory@^2.1.1": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "require-from-string@^2.0.2": "https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz", "requireg@^0.2.2": "https://registry.npmjs.org/requireg/-/requireg-0.2.2.tgz", "resolve-from@^3.0.0": "https://registry.npmjs.org/resolve-from/-/resolve-from-3.0.0.tgz", "resolve-from@^5.0.0": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz", "resolve-workspace-root@^2.0.0": "https://registry.npmjs.org/resolve-workspace-root/-/resolve-workspace-root-2.0.0.tgz", "resolve.exports@^2.0.3": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.3.tgz", "resolve@^1.14.2": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@^1.22.2": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "resolve@~1.7.1": "https://registry.npmjs.org/resolve/-/resolve-1.7.1.tgz", "restore-cursor@^2.0.0": "https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz", "rimraf@^3.0.2": "https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz", "safe-buffer@5.2.1": "https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz", "sax@>=0.6.0": "https://registry.npmjs.org/sax/-/sax-1.4.1.tgz", "scheduler@0.25.0": "https://registry.npmjs.org/scheduler/-/scheduler-0.25.0.tgz", "semver@^6.3.0": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "semver@^6.3.1": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "semver@^7.1.3": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "semver@^7.3.5": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "semver@^7.5.4": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "semver@^7.6.0": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "send@0.19.0": "https://registry.npmjs.org/send/-/send-0.19.0.tgz", "send@^0.19.0": "https://registry.npmjs.org/send/-/send-0.19.1.tgz", "serialize-error@^2.1.0": "https://registry.npmjs.org/serialize-error/-/serialize-error-2.1.0.tgz", "serve-static@^1.16.2": "https://registry.npmjs.org/serve-static/-/serve-static-1.16.2.tgz", "setprototypeof@1.2.0": "https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz", "shebang-command@^2.0.0": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "shebang-regex@^3.0.0": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "shell-quote@^1.6.1": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.3.tgz", "signal-exit@^3.0.2": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^3.0.7": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "signal-exit@^4.0.1": "https://registry.npmjs.org/signal-exit/-/signal-exit-4.1.0.tgz", "simple-plist@^1.1.0": "https://registry.npmjs.org/simple-plist/-/simple-plist-1.3.1.tgz", "simple-swizzle@^0.2.2": "https://registry.npmjs.org/simple-swizzle/-/simple-swizzle-0.2.2.tgz", "sisteransi@^1.0.5": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz", "slash@^3.0.0": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "slugify@^1.3.4": "https://registry.npmjs.org/slugify/-/slugify-1.6.6.tgz", "slugify@^1.6.6": "https://registry.npmjs.org/slugify/-/slugify-1.6.6.tgz", "source-map-js@^1.2.1": "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz", "source-map-support@~0.5.20": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "source-map-support@~0.5.21": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz", "source-map@^0.5.6": "https://registry.npmjs.org/source-map/-/source-map-0.5.7.tgz", "source-map@^0.6.0": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "source-map@^0.6.1": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "split-on-first@^1.0.0": "https://registry.npmjs.org/split-on-first/-/split-on-first-1.1.0.tgz", "sprintf-js@~1.0.2": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "stack-utils@^2.0.3": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz", "stackframe@^1.3.4": "https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz", "stacktrace-parser@^0.1.10": "https://registry.npmjs.org/stacktrace-parser/-/stacktrace-parser-0.1.11.tgz", "statuses@2.0.1": "https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz", "statuses@~1.5.0": "https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz", "stream-buffers@2.2.x": "https://registry.npmjs.org/stream-buffers/-/stream-buffers-2.2.0.tgz", "strict-uri-encode@^2.0.0": "https://registry.npmjs.org/strict-uri-encode/-/strict-uri-encode-2.0.0.tgz", "string-width-cjs@npm:string-width@^4.2.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.1.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.0": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^4.2.3": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "string-width@^5.0.1": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz", "string-width@^5.1.2": "https://registry.npmjs.org/string-width/-/string-width-5.1.2.tgz", "strip-ansi-cjs@npm:strip-ansi@^6.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^5.2.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-5.2.0.tgz", "strip-ansi@^6.0.0": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^6.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "strip-ansi@^7.0.1": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-7.1.0.tgz", "strip-json-comments@~2.0.1": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "structured-headers@^0.4.1": "https://registry.npmjs.org/structured-headers/-/structured-headers-0.4.1.tgz", "sucrase@3.35.0": "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz", "supports-color@^5.3.0": "https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz", "supports-color@^7.0.0": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "supports-color@^7.1.0": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "supports-color@^8.0.0": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz", "supports-hyperlinks@^2.0.0": "https://registry.npmjs.org/supports-hyperlinks/-/supports-hyperlinks-2.3.0.tgz", "supports-preserve-symlinks-flag@^1.0.0": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "tar@^7.4.3": "https://registry.npmjs.org/tar/-/tar-7.4.3.tgz", "temp-dir@~2.0.0": "https://registry.npmjs.org/temp-dir/-/temp-dir-2.0.0.tgz", "terminal-link@^2.1.1": "https://registry.npmjs.org/terminal-link/-/terminal-link-2.1.1.tgz", "terser@^5.15.0": "https://registry.npmjs.org/terser/-/terser-5.42.0.tgz", "test-exclude@^6.0.0": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz", "thenify-all@^1.0.0": "https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz", "thenify@>= 3.1.0 < 4": "https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz", "throat@^5.0.0": "https://registry.npmjs.org/throat/-/throat-5.0.0.tgz", "tmpl@1.0.5": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz", "to-regex-range@^5.0.1": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "toidentifier@1.0.1": "https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz", "tr46@~0.0.3": "https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz", "ts-interface-checker@^0.1.9": "https://registry.npmjs.org/ts-interface-checker/-/ts-interface-checker-0.1.13.tgz", "type-detect@4.0.8": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz", "type-fest@^0.21.3": "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz", "type-fest@^0.7.1": "https://registry.npmjs.org/type-fest/-/type-fest-0.7.1.tgz", "undici-types@~6.21.0": "https://registry.npmjs.org/undici-types/-/undici-types-6.21.0.tgz", "undici@^6.18.2": "https://registry.npmjs.org/undici/-/undici-6.21.3.tgz", "unicode-canonical-property-names-ecmascript@^2.0.0": "https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.1.tgz", "unicode-match-property-ecmascript@^2.0.0": "https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz", "unicode-match-property-value-ecmascript@^2.1.0": "https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.2.0.tgz", "unicode-property-aliases-ecmascript@^2.0.0": "https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz", "unique-string@~2.0.0": "https://registry.npmjs.org/unique-string/-/unique-string-2.0.0.tgz", "unpipe@~1.0.0": "https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz", "update-browserslist-db@^1.1.3": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "use-latest-callback@^0.2.3": "https://registry.npmjs.org/use-latest-callback/-/use-latest-callback-0.2.3.tgz", "use-sync-external-store@^1.5.0": "https://registry.npmjs.org/use-sync-external-store/-/use-sync-external-store-1.5.0.tgz", "utils-merge@1.0.1": "https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz", "uuid@^7.0.3": "https://registry.npmjs.org/uuid/-/uuid-7.0.3.tgz", "validate-npm-package-name@^5.0.0": "https://registry.npmjs.org/validate-npm-package-name/-/validate-npm-package-name-5.0.1.tgz", "vary@~1.1.2": "https://registry.npmjs.org/vary/-/vary-1.1.2.tgz", "vlq@^1.0.0": "https://registry.npmjs.org/vlq/-/vlq-1.0.1.tgz", "walker@^1.0.7": "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz", "walker@^1.0.8": "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz", "warn-once@0.1.1": "https://registry.npmjs.org/warn-once/-/warn-once-0.1.1.tgz", "warn-once@^0.1.0": "https://registry.npmjs.org/warn-once/-/warn-once-0.1.1.tgz", "wcwidth@^1.0.1": "https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz", "webidl-conversions@^3.0.0": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz", "webidl-conversions@^5.0.0": "https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-5.0.0.tgz", "whatwg-fetch@^3.0.0": "https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.20.tgz", "whatwg-url-without-unicode@8.0.0-3": "https://registry.npmjs.org/whatwg-url-without-unicode/-/whatwg-url-without-unicode-8.0.0-3.tgz", "whatwg-url@^5.0.0": "https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz", "which@^2.0.1": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "wonka@^6.3.2": "https://registry.npmjs.org/wonka/-/wonka-6.3.5.tgz", "wrap-ansi-cjs@npm:wrap-ansi@^7.0.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^7.0.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "wrap-ansi@^8.1.0": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-8.1.0.tgz", "wrappy@1": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "write-file-atomic@^4.0.2": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "ws@^6.2.3": "https://registry.npmjs.org/ws/-/ws-6.2.3.tgz", "ws@^7": "https://registry.npmjs.org/ws/-/ws-7.5.10.tgz", "ws@^7.5.10": "https://registry.npmjs.org/ws/-/ws-7.5.10.tgz", "ws@^8.12.1": "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz", "ws@^8.18.2": "https://registry.npmjs.org/ws/-/ws-8.18.2.tgz", "xcode@^3.0.1": "https://registry.npmjs.org/xcode/-/xcode-3.0.1.tgz", "xml2js@0.6.0": "https://registry.npmjs.org/xml2js/-/xml2js-0.6.0.tgz", "xmlbuilder@^15.1.1": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-15.1.1.tgz", "xmlbuilder@~11.0.0": "https://registry.npmjs.org/xmlbuilder/-/xmlbuilder-11.0.1.tgz", "y18n@^5.0.5": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "yallist@^3.0.2": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "yallist@^5.0.0": "https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz", "yargs-parser@^21.1.1": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "yargs@^17.6.2": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "yocto-queue@^0.1.0": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"}, "files": [], "artifacts": {}}