
/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen)
 * and copied to the cpp directory to add custom state and set shadow node trait as a LeafYogaNode.
 *
 * @generated by codegen project: GenerateShadowNodeH.js
 */

#pragma once

#include "RNDateTimePickerState.h"
#include <react/renderer/components/RNDateTimePickerCGen/EventEmitters.h>
#include <react/renderer/components/RNDateTimePickerCGen/Props.h>
#include <react/renderer/components/view/ConcreteViewShadowNode.h>
#include <jsi/jsi.h>
#include <react/renderer/core/LayoutContext.h>

namespace facebook {
namespace react {

JSI_EXPORT extern const char RNDateTimePickerComponentName[];

/*
 * `ShadowNode` for <RNDateTimePicker> component.
 */
class JSI_EXPORT RNDateTimePickerShadowNode final : public ConcreteViewShadowNode<RNDateTimePickerComponentName, R<PERSON>ateTimePickerP<PERSON>, R<PERSON>ateTimePickerEventEmitter, RNDateTimePickerState> {

public:
    using ConcreteViewShadowNode::ConcreteViewShadowNode;

    static ShadowNodeTraits BaseTraits() {
        auto traits = ConcreteViewShadowNode::BaseTraits();
        traits.set(ShadowNodeTraits::Trait::LeafYogaNode);
        return traits;
    }
};

} // namespace react
} // namespace facebook
