/** @license React v16.14.0
 * react-dom-test-utils.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
'use strict';(function(k,n){"object"===typeof exports&&"undefined"!==typeof module?module.exports=n(require("react"),require("react-dom")):"function"===typeof define&&define.amd?define(["react","react-dom"],n):(k=k||self,k.ReactTestUtils=n(k.React,k.ReactDOM))})(this,function(k,n){function l(a){for(var b="https://reactjs.org/docs/error-decoder.html?invariant="+a,c=1;c<arguments.length;c++)b+="&args[]="+encodeURIComponent(arguments[c]);return"Minified React error #"+a+"; visit "+b+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}
function F(a){var b=a,c=a;if(a.alternate)for(;b.return;)b=b.return;else{a=b;do b=a,0!==(b.effectTag&1026)&&(c=b.return),a=b.return;while(a)}return 3===b.tag?c:null}function G(a){if(F(a)!==a)throw Error(l(188));}function R(a){var b=a.alternate;if(!b){b=F(a);if(null===b)throw Error(l(188));return b!==a?null:a}for(var c=a,e=b;;){var d=c.return;if(null===d)break;var g=d.alternate;if(null===g){e=d.return;if(null!==e){c=e;continue}break}if(d.child===g.child){for(g=d.child;g;){if(g===c)return G(d),a;if(g===
e)return G(d),b;g=g.sibling}throw Error(l(188));}if(c.return!==e.return)c=d,e=g;else{for(var f=!1,m=d.child;m;){if(m===c){f=!0;c=d;e=g;break}if(m===e){f=!0;e=d;c=g;break}m=m.sibling}if(!f){for(m=g.child;m;){if(m===c){f=!0;c=g;e=d;break}if(m===e){f=!0;e=g;c=d;break}m=m.sibling}if(!f)throw Error(l(189));}}if(c.alternate!==e)throw Error(l(190));}if(3!==c.tag)throw Error(l(188));return c.stateNode.current===c?a:b}function u(){return!0}function v(){return!1}function r(a,b,c,e){this.dispatchConfig=a;this._targetInst=
b;this.nativeEvent=c;a=this.constructor.Interface;for(var d in a)a.hasOwnProperty(d)&&((b=a[d])?this[d]=b(c):"target"===d?this.target=e:this[d]=c[d]);this.isDefaultPrevented=(null!=c.defaultPrevented?c.defaultPrevented:!1===c.returnValue)?u:v;this.isPropagationStopped=v;return this}function S(a,b,c,e){if(this.eventPool.length){var d=this.eventPool.pop();this.call(d,a,b,c,e);return d}return new this(a,b,c,e)}function T(a){if(!(a instanceof this))throw Error(l(279));a.destructor();10>this.eventPool.length&&
this.eventPool.push(a)}function H(a){a.eventPool=[];a.getPooled=S;a.release=T}function w(a,b){var c={};c[a.toLowerCase()]=b.toLowerCase();c["Webkit"+a]="webkit"+b;c["Moz"+a]="moz"+b;return c}function x(a){if(A[a])return A[a];if(!q[a])return a;var b=q[a],c;for(c in b)if(b.hasOwnProperty(c)&&c in I)return A[a]=b[c];return a}function U(a){if(null===y)try{var b=("require"+Math.random()).slice(0,7);y=(module&&module[b])("timers").setImmediate}catch(c){y=function(a){var b=new MessageChannel;b.port1.onmessage=
a;b.port2.postMessage(void 0)}}return y(a)}function J(a){try{B(),U(function(){B()?J(a):a()})}catch(b){a(b)}}function K(a){}function V(a,b){if(!a)return[];a=R(a);if(!a)return[];for(var c=a,e=[];;){if(5===c.tag||6===c.tag||1===c.tag||0===c.tag){var d=c.stateNode;b(d)&&e.push(d)}if(c.child)c.child.return=c,c=c.child;else{if(c===a)return e;for(;!c.sibling;){if(!c.return||c.return===a)return e;c=c.return}c.sibling.return=c.return;c=c.sibling}}}function p(a,b){if(a&&!a._reactInternalFiber){var c=""+a;a=
Array.isArray(a)?"an array":a&&1===a.nodeType&&a.tagName?"a DOM node":"[object Object]"===c?"object with keys {"+Object.keys(a).join(", ")+"}":c;throw Error(l(286,b,a));}}function W(a){return function(b,c){if(k.isValidElement(b))throw Error(l(228));if(f.isCompositeComponent(b))throw Error(l(229));var e=L[a],d=new K;d.target=b;d.type=a.toLowerCase();var g=X(b),h=new r(e,g,d,b);h.persist();t(h,c);e.phasedRegistrationNames?Y(h):Z(h);n.unstable_batchedUpdates(function(){aa(b);ba(h)});ca()}}function da(a,
b){return function(c,e){var d=new K(a);t(d,e);f.isDOMComponent(c)?(c=ea(c),d.target=c,M(b,1,document,d)):c.tagName&&(d.target=c,M(b,1,document,d))}}var t=k.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.assign,h=k.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;h.hasOwnProperty("ReactCurrentDispatcher")||(h.ReactCurrentDispatcher={current:null});h.hasOwnProperty("ReactCurrentBatchConfig")||(h.ReactCurrentBatchConfig={suspense:null});t(r.prototype,{preventDefault:function(){this.defaultPrevented=
!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():"unknown"!==typeof a.returnValue&&(a.returnValue=!1),this.isDefaultPrevented=u)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():"unknown"!==typeof a.cancelBubble&&(a.cancelBubble=!0),this.isPropagationStopped=u)},persist:function(){this.isPersistent=u},isPersistent:v,destructor:function(){var a=this.constructor.Interface,b;for(b in a)this[b]=null;this.nativeEvent=this._targetInst=this.dispatchConfig=
null;this.isPropagationStopped=this.isDefaultPrevented=v;this._dispatchInstances=this._dispatchListeners=null}});r.Interface={type:null,target:null,currentTarget:function(){return null},eventPhase:null,bubbles:null,cancelable:null,timeStamp:function(a){return a.timeStamp||Date.now()},defaultPrevented:null,isTrusted:null};r.extend=function(a){function b(){return c.apply(this,arguments)}var c=this,e=function(){};e.prototype=c.prototype;e=new e;t(e,b.prototype);b.prototype=e;b.prototype.constructor=
b;b.Interface=t({},c.Interface,a);b.extend=c.extend;H(b);return b};H(r);var C=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),q={animationend:w("Animation","AnimationEnd"),animationiteration:w("Animation","AnimationIteration"),animationstart:w("Animation","AnimationStart"),transitionend:w("Transition","TransitionEnd")},A={},I={};C&&(I=document.createElement("div").style,"AnimationEvent"in window||(delete q.animationend.animation,
delete q.animationiteration.animation,delete q.animationstart.animation),"TransitionEvent"in window||delete q.transitionend.transition);C=x("animationend");var fa=x("animationiteration"),ha=x("animationstart"),ia=x("transitionend"),y=null,N=k.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Scheduler.unstable_flushAllWithoutAsserting,O=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Events,ja=O[11],D=O[12],ka=n.unstable_batchedUpdates,E=h.IsSomeRendererActing,P="function"===typeof N,B=N||function(){for(var a=
!1;ja();)a=!0;return a},z=0,Q=!1,ea=n.findDOMNode;h=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Events;var X=h[0],L=h[4],Y=h[5],Z=h[6],aa=h[7],ca=h[8],M=h[9],ba=h[10],f={renderIntoDocument:function(a){var b=document.createElement("div");return n.render(a,b)},isElement:function(a){return k.isValidElement(a)},isElementOfType:function(a,b){return k.isValidElement(a)&&a.type===b},isDOMComponent:function(a){return!(!a||1!==a.nodeType||!a.tagName)},isDOMComponentElement:function(a){return!!(a&&
k.isValidElement(a)&&a.tagName)},isCompositeComponent:function(a){return f.isDOMComponent(a)?!1:null!=a&&"function"===typeof a.render&&"function"===typeof a.setState},isCompositeComponentWithType:function(a,b){return f.isCompositeComponent(a)?a._reactInternalFiber.type===b:!1},findAllInRenderedTree:function(a,b){p(a,"findAllInRenderedTree");return a?V(a._reactInternalFiber,b):[]},scryRenderedDOMComponentsWithClass:function(a,b){p(a,"scryRenderedDOMComponentsWithClass");return f.findAllInRenderedTree(a,
function(a){if(f.isDOMComponent(a)){var c=a.className;"string"!==typeof c&&(c=a.getAttribute("class")||"");var d=c.split(/\s+/);if(!Array.isArray(b)){if(void 0===b)throw Error(l(11));b=b.split(/\s+/)}return b.every(function(a){return-1!==d.indexOf(a)})}return!1})},findRenderedDOMComponentWithClass:function(a,b){p(a,"findRenderedDOMComponentWithClass");a=f.scryRenderedDOMComponentsWithClass(a,b);if(1!==a.length)throw Error("Did not find exactly one match (found: "+a.length+") for class:"+b);return a[0]},
scryRenderedDOMComponentsWithTag:function(a,b){p(a,"scryRenderedDOMComponentsWithTag");return f.findAllInRenderedTree(a,function(a){return f.isDOMComponent(a)&&a.tagName.toUpperCase()===b.toUpperCase()})},findRenderedDOMComponentWithTag:function(a,b){p(a,"findRenderedDOMComponentWithTag");a=f.scryRenderedDOMComponentsWithTag(a,b);if(1!==a.length)throw Error("Did not find exactly one match (found: "+a.length+") for tag:"+b);return a[0]},scryRenderedComponentsWithType:function(a,b){p(a,"scryRenderedComponentsWithType");
return f.findAllInRenderedTree(a,function(a){return f.isCompositeComponentWithType(a,b)})},findRenderedComponentWithType:function(a,b){p(a,"findRenderedComponentWithType");a=f.scryRenderedComponentsWithType(a,b);if(1!==a.length)throw Error("Did not find exactly one match (found: "+a.length+") for componentType:"+b);return a[0]},mockComponent:function(a,b){b=b||a.mockTagName||"div";a.prototype.render.mockImplementation(function(){return k.createElement(b,null,this.props.children)});return this},nativeTouchData:function(a,
b){return{touches:[{pageX:a,pageY:b}]}},Simulate:null,SimulateNative:{},act:function(a){function b(){z--;E.current=c;D.current=e}!1===Q&&(Q=!0,console.error("act(...) is not supported in production builds of React, and might not behave as expected."));z++;var c=E.current;var e=D.current;E.current=!0;D.current=!0;try{var d=ka(a)}catch(g){throw b(),g;}if(null!==d&&"object"===typeof d&&"function"===typeof d.then)return{then:function(a,e){d.then(function(){1<z||!0===P&&!0===c?(b(),a()):J(function(c){b();
c?e(c):a()})},function(a){b();e(a)})}};try{1!==z||!1!==P&&!1!==c||B(),b()}catch(g){throw b(),g;}return{then:function(a){a()}}}};(function(){f.Simulate={};for(var a in L)f.Simulate[a]=W(a)})();[["abort","abort"],[C,"animationEnd"],[fa,"animationIteration"],[ha,"animationStart"],["blur","blur"],["canplaythrough","canPlayThrough"],["canplay","canPlay"],["cancel","cancel"],["change","change"],["click","click"],["close","close"],["compositionend","compositionEnd"],["compositionstart","compositionStart"],
["compositionupdate","compositionUpdate"],["contextmenu","contextMenu"],["copy","copy"],["cut","cut"],["dblclick","doubleClick"],["dragend","dragEnd"],["dragenter","dragEnter"],["dragexit","dragExit"],["dragleave","dragLeave"],["dragover","dragOver"],["dragstart","dragStart"],["drag","drag"],["drop","drop"],["durationchange","durationChange"],["emptied","emptied"],["encrypted","encrypted"],["ended","ended"],["error","error"],["focus","focus"],["input","input"],["keydown","keyDown"],["keypress","keyPress"],
["keyup","keyUp"],["loadstart","loadStart"],["loadstart","loadStart"],["load","load"],["loadeddata","loadedData"],["loadedmetadata","loadedMetadata"],["mousedown","mouseDown"],["mousemove","mouseMove"],["mouseout","mouseOut"],["mouseover","mouseOver"],["mouseup","mouseUp"],["paste","paste"],["pause","pause"],["play","play"],["playing","playing"],["progress","progress"],["ratechange","rateChange"],["scroll","scroll"],["seeked","seeked"],["seeking","seeking"],["selectionchange","selectionChange"],["stalled",
"stalled"],["suspend","suspend"],["textInput","textInput"],["timeupdate","timeUpdate"],["toggle","toggle"],["touchcancel","touchCancel"],["touchend","touchEnd"],["touchmove","touchMove"],["touchstart","touchStart"],[ia,"transitionEnd"],["volumechange","volumeChange"],["waiting","waiting"],["wheel","wheel"]].forEach(function(a){var b=a[1];f.SimulateNative[b]=da(b,a[0])});return f.default||f});
